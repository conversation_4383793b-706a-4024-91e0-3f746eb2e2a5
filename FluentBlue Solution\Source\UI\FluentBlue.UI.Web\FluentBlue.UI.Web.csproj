﻿<Project Sdk="Microsoft.NET.Sdk.BlazorWebAssembly">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <ServiceWorkerAssetsManifest>service-worker-assets.js</ServiceWorkerAssetsManifest>
    <PlatformTarget>AnyCPU</PlatformTarget>
	<BlazorWebAssemblyLoadAllGlobalizationData>true</BlazorWebAssemblyLoadAllGlobalizationData>
	<PublishAot>False</PublishAot>
	<AssemblyVersion>1.0.0.2</AssemblyVersion>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
	<DebugType>portable</DebugType>
	<DebugSymbols>true</DebugSymbols>
	<GenerateMapFile>true</GenerateMapFile>
    <GenerateAssemblyInfo>True</GenerateAssemblyInfo>
    <RunAOTCompilation>False</RunAOTCompilation>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
    <GenerateAssemblyInfo>True</GenerateAssemblyInfo>
    <RunAOTCompilation>True</RunAOTCompilation>
    <BlazorEnableCompression>true</BlazorEnableCompression>
    <DebugType>none</DebugType>
    <DebugSymbols>false</DebugSymbols>
    <EnableTrimming>true</EnableTrimming>
    <TrimMode>partial</TrimMode>
    <WasmNativeStrip>true</WasmNativeStrip>
    <EmccCompileOptimizationFlag>-O3</EmccCompileOptimizationFlag>
    <WasmEmitSymbolMap>false</WasmEmitSymbolMap>
    <InvariantGlobalization>false</InvariantGlobalization>
    <BlazorWebAssemblyPreserveCollationData>true</BlazorWebAssemblyPreserveCollationData>
    <OptimizationLevel>3</OptimizationLevel>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="Layout\**" />
    <Compile Remove="Pages\**" />
    <Content Remove="Layout\**" />
    <Content Remove="Pages\**" />
    <EmbeddedResource Remove="Layout\**" />
    <EmbeddedResource Remove="Pages\**" />
    <None Remove="Layout\**" />
    <None Remove="Pages\**" />
  </ItemGroup>

  <ItemGroup>
    <Content Remove="wwwroot\css\app.css" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.Components.WebAssembly" Version="9.0.9" />
    <PackageReference Include="Microsoft.AspNetCore.Components.WebAssembly.DevServer" Version="9.0.9" PrivateAssets="all" />
    <PackageReference Include="Microsoft.FluentUI.AspNetCore.Components" Version="4.12.1" />
    <PackageReference Include="Sentry.AspNetCore.Blazor.WebAssembly" Version="5.15.0" />
    <PackageReference Include="Sentry.Serilog" Version="5.15.0" />
    <PackageReference Include="Serilog.Extensions.Logging" Version="9.0.2" />
    <PackageReference Include="Serilog.Sinks.Console" Version="6.0.0" />
    <PackageReference Include="Soenneker.Serilog.Sinks.Browser.Blazor" Version="3.0.193" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\FluentBlue.UI.Main\FluentBlue.UI.Main.csproj" />
  </ItemGroup>

  <ItemGroup>
    <ServiceWorker Include="wwwroot\service-worker.js" PublishedContent="wwwroot\service-worker.published.js" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="wwwroot\css\" />
    <Folder Include="wwwroot\images\" />
  </ItemGroup>

	<ItemGroup>
		<Content Include="appsettings.json" />
	</ItemGroup>

	<ItemGroup>
	  <Content Update="wwwroot\appsettings.Development.json">
	    <CopyToOutputDirectory>Always</CopyToOutputDirectory>
	  </Content>
	  <Content Update="wwwroot\appsettings.Production.json">
	    <CopyToOutputDirectory>Always</CopyToOutputDirectory>
	  </Content>
	  <Content Update="wwwroot\deployment-guid.json">
	    <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
	  </Content>
	</ItemGroup>
	
	<!-- Custom target to generate guid.txt in Release configuration -->
	<Target Name="GenerateGuidFile" AfterTargets="Build" Condition="'$(Configuration)' == 'Release'">
		<PropertyGroup>
			<!-- Define the output file path -->
			<GuidFilePath>$(OutputPath)wwwroot\deployment-guid.json</GuidFilePath>
			<!-- Generate a new GUID -->
			<NewGuid>$([System.Guid]::NewGuid().ToString())</NewGuid>
		</PropertyGroup>
		<!-- Write the GUID to guid.txt -->
		<WriteLinesToFile File="$(GuidFilePath)" Lines="$(NewGuid)" Overwrite="true" Encoding="UTF-8" />
		<Message Text="Generated deployment-guid.json with GUID: $(NewGuid)" Importance="High" />
	</Target>
</Project>