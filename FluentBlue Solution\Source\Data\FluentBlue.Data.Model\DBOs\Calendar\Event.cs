﻿using FluentBlue.Data.Model.DBOs.Contacts;
using FluentBlue.Data.Model.DBOs.Tenants;
using Microsoft.Extensions.Options;
using NodaTime;
using System;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Diagnostics.CodeAnalysis;
using System.Net;
using System.Runtime.CompilerServices;


namespace FluentBlue.Data.Model.DBOs.Calendar
{
    [Table("Event", Schema = "Calendar")]
    public class Event : IObjectState, INotifyPropertyChanged
    {
        private Guid eventId;
        private Guid? tenantId;
        private Guid? contactId;
        private Guid? eventCategoryId;
        private Guid? eventStateId;
        private string subject;
        private string location;
        private DateTime startTimeUtc;
        private string? startTimeZone;
        private DateTime endTimeUtc;
        private string? endTimeZone;
        private string description;
        private bool allDay;
        private bool readOnly;
        private bool block;
        private bool customRecurrence;
        private Guid? customRecurrenceId;
        private string customRecurrenceRule;
        private DateTime? reminderTimeUtc;
        private bool? reminderDismissed;
        private DateTime dateCreatedUtc;
        private DateTime dateModifiedUtc;

        public Event()
        {
            eventId = Guid.CreateVersion7();
            subject = string.Empty;
            location = string.Empty;
            startTimeZone = null;
            endTimeZone = null;
            description = string.Empty;
            allDay = false;
            block = false;
            customRecurrenceId = null;
            customRecurrenceRule = string.Empty;
            customRecurrence = false;
            reminderTimeUtc = null;
            reminderDismissed = null;
            readOnly = false;
            DateCreatedUtc = DateTime.UtcNow;
            DateModifiedUtc = DateTime.UtcNow;
            RowVersion = Array.Empty<byte>();
            ObjectState = ObjectState.Unchanged;
            EventUsers = new List<EventUser>();
            EventReminders = new List<EventReminder>();
        }

        [Key]
        [Display(ResourceType = typeof(Model.Resources.Calendar.EventResource), Name = nameof(Model.Resources.Calendar.EventResource.EventId))]
        public Guid EventId
        {
            get
            {
                return eventId;
            }
            set
            {
                eventId = value;
                NotifyPropertyChanged();
            }
        }   //TODO: Στη βάση δεδομένων πρέπει το primary key να γίνει από non-clustered index σε clustered για λόγους ταχύτητας (τα GUID ειναι αργά με Clustered index).

        [Required]
        public Guid? TenantId
        {
            get
            {
                return tenantId;
            }
            set
            {
                tenantId = value;
                NotifyPropertyChanged();
            }
        }

        [ForeignKey("TenantId")]
        public Tenant? Tenant { get; set; }


        public Guid? EventCategoryId
        {
            get
            {
                return eventCategoryId ?? Guid.Empty;
            }
            set
            {
                eventCategoryId = (value == Guid.Empty ? null : value);
                NotifyPropertyChanged();
            }
        }

        [ForeignKey("EventCategoryId")]
        public EventCategory? EventCategory { get; set; }

        public Guid? EventStateId
        {
            get
            {
                return eventStateId ?? Guid.Empty;
            }
            set
            {
                eventStateId = (value == Guid.Empty ? null : value);
                NotifyPropertyChanged();
            }
        }

        [ForeignKey("EventStateId")]
        public EventState? EventState { get; set; }

        [Display(ResourceType = typeof(Model.Resources.Calendar.EventResource), Name = nameof(Model.Resources.Calendar.EventResource.ContactId))]
        public Guid? ContactId
        {
            get
            {
                return contactId;
            }
            set
            {
                contactId = (value == Guid.Empty ? null : value);
                NotifyPropertyChanged();
            }
        }

        [ForeignKey("ContactId")]
        public Contact? Contact { get; set; }

        [Display(ResourceType = typeof(Model.Resources.Calendar.EventResource), Name = nameof(Model.Resources.Calendar.EventResource.Subject))]
        [MaxLength(300, ErrorMessageResourceType = typeof(Model.Resources.Calendar.EventResource), ErrorMessageResourceName = nameof(Model.Resources.Calendar.EventResource.SubjectMaxLengthError))]
        public string Subject
        {
            get
            {
                return subject;
            }
            set
            {
                subject = value ?? "";
                NotifyPropertyChanged();
            }
        }

        [Display(ResourceType = typeof(Model.Resources.Calendar.EventResource), Name = nameof(Model.Resources.Calendar.EventResource.Location))]
        [MaxLength(300, ErrorMessageResourceType = typeof(Model.Resources.Calendar.EventResource), ErrorMessageResourceName = nameof(Model.Resources.Calendar.EventResource.SubjectMaxLengthError))]
        public string Location
        {
            get
            {
                return location;
            }
            set
            {
                location = value ?? "";
                NotifyPropertyChanged();
            }
        }

        [Required(ErrorMessageResourceName = nameof(Model.Resources.GeneralValidationResource.FieldRequired), ErrorMessageResourceType = typeof(Model.Resources.GeneralValidationResource))]
        [Display(ResourceType = typeof(Model.Resources.Calendar.EventResource), Name = nameof(Model.Resources.Calendar.EventResource.StartTime))]
        [Column(TypeName = "datetime")]
        public DateTime StartTimeUtc
        {
            get => DateTime.SpecifyKind(startTimeUtc, DateTimeKind.Utc);
            set
            {
                var utcValue = EnsureUtc(value);

                if (startTimeUtc != utcValue)
                {
                    //startTimeUtc = utcValue;
                    //if (AllDay) NormalizeToAllDayTimes();
                    startTimeUtc = AllDay ? NormalizeUtcDateTimeForAllDay(utcValue) : utcValue;

                    NotifyPropertyChanged();
                    NotifyPropertyChanged(nameof(StartTimeLocal));
                }
            }
        }

        [NotMapped]
        [Required(ErrorMessageResourceName = nameof(Model.Resources.GeneralValidationResource.FieldRequired), ErrorMessageResourceType = typeof(Model.Resources.GeneralValidationResource))]
        [Display(ResourceType = typeof(Model.Resources.Calendar.EventResource), Name = nameof(Model.Resources.Calendar.EventResource.StartTime))]
        public DateTime? StartTimeLocal
        {
            get
            {
                var tz = GetUserTimeZone();
                if (tz == null) return null;

                var localTime = TimeZoneInfo.ConvertTimeFromUtc(startTimeUtc, tz);
                return AllDay ? localTime.Date : localTime;
            }
            set
            {
                if (value == null) return;

                var tz = GetUserTimeZone();
                if (tz == null) return;

                //if (value.Value.Kind == DateTimeKind.Utc)
                //    throw new ArgumentException("Local times must be provided with DateTimeKind.Unspecified or DateTimeKind.Utc", nameof(StartTimeLocal));
                value = DateTime.SpecifyKind(value.Value, DateTimeKind.Unspecified);

                if (AllDay)
                    StartTimeUtc = TimeZoneInfo.ConvertTimeToUtc(value.Value.Date, tz);
                else
                    StartTimeUtc = TimeZoneInfo.ConvertTimeToUtc(value.Value, tz);
            }
        }

        [Display(ResourceType = typeof(Model.Resources.Calendar.EventResource), Name = nameof(Model.Resources.Calendar.EventResource.StartTimeZone))]
        public string? StartTimeZone
        {
            get
            {
                //return this.UserTimeZoneId;
                return "";
            }
            set
            {
                //var temp = value ?? "";
                //startTimeZone = value ?? "";
                //NotifyPropertyChanged();
            }
        }


        [Required(ErrorMessageResourceName = nameof(Model.Resources.GeneralValidationResource.FieldRequired), ErrorMessageResourceType = typeof(Model.Resources.GeneralValidationResource))]
        [Display(ResourceType = typeof(Model.Resources.Calendar.EventResource), Name = nameof(Model.Resources.Calendar.EventResource.EndTime))]
        [Column(TypeName = "datetime")]
        public DateTime EndTimeUtc
        {
            get => DateTime.SpecifyKind(endTimeUtc, DateTimeKind.Utc);
            set
            {
                var utcValue = EnsureUtc(value);

                if (endTimeUtc != utcValue)
                {
                    //endTimeUtc = utcValue;
                    //if (AllDay) NormalizeToAllDayTimes();
                    endTimeUtc = AllDay ? NormalizeUtcDateTimeForAllDay(utcValue) : utcValue;

                    NotifyPropertyChanged();
                    NotifyPropertyChanged(nameof(EndTimeLocal));
                }
            }
        }

        [NotMapped]
        //[Required(ErrorMessageResourceName = nameof(Model.Resources.GeneralValidationResource.FieldRequired), ErrorMessageResourceType = typeof(Model.Resources.GeneralValidationResource))]
        [Display(ResourceType = typeof(Model.Resources.Calendar.EventResource), Name = nameof(Model.Resources.Calendar.EventResource.EndTime))]
        public DateTime? EndTimeLocal
        {
            get
            {
                var tz = GetUserTimeZone();
                if (tz == null) return null;

                var localTime = TimeZoneInfo.ConvertTimeFromUtc(endTimeUtc, tz);
                return AllDay ? localTime.Date : localTime;
            }
            set
            {
                if (value == null) return;

                var tz = GetUserTimeZone();
                if (tz == null) return;

                //if (value.Value.Kind != DateTimeKind.Unspecified)
                //    throw new ArgumentException("Local times must be provided with DateTimeKind.Unspecified", nameof(EndTimeLocal));
                value = DateTime.SpecifyKind(value.Value, DateTimeKind.Unspecified);

                if (AllDay)
                {
                    var endOfDay = value.Value.Date.AddDays(1).Date;
                    EndTimeUtc = TimeZoneInfo.ConvertTimeToUtc(endOfDay, tz);
                }
                else
                {
                    EndTimeUtc = TimeZoneInfo.ConvertTimeToUtc(value.Value, tz);
                }
            }
        }

        [Display(ResourceType = typeof(Model.Resources.Calendar.EventResource), Name = nameof(Model.Resources.Calendar.EventResource.EndTimeZone))]
        public string? EndTimeZone
        {
            get
            {
                //return this.UserTimeZoneId;
                return "";
            }
            set
            {
                //var temp = value ?? "";
                //endTimeZone = value ?? "";
                //NotifyPropertyChanged();
            }
        }

        [NotMapped]
        public string? TimeZoneLocal
        {
            get
            {
                return this.UserTimeZoneId;
            }
            set
            {
                //var temp = value ?? "";
            }
        }

        [Display(ResourceType = typeof(Model.Resources.Calendar.EventResource), Name = nameof(Model.Resources.Calendar.EventResource.Description))]
        [MaxLength(2000, ErrorMessageResourceType = typeof(Model.Resources.Calendar.EventResource), ErrorMessageResourceName = nameof(Model.Resources.Calendar.EventResource.DescriptionMaxLengthError))]
        public string Description
        {
            get
            {
                return description;
            }
            set
            {
                description = value ?? "";
                NotifyPropertyChanged();
            }
        }

        [Display(ResourceType = typeof(Model.Resources.Calendar.EventResource), Name = nameof(Model.Resources.Calendar.EventResource.Description))]
        public string PlainDescription
        {
            get
            {
                if (description != null && description.Length > 0)
                {
                    return System.Text.RegularExpressions.Regex.Replace(Description, "<[^>]*>", "").Replace("&nbsp;", " ");
                }
                else
                {
                    return "";
                }

            }
        }

        [Display(ResourceType = typeof(Model.Resources.Calendar.EventResource), Name = nameof(Model.Resources.Calendar.EventResource.AllDay))]
        public bool AllDay
        {
            get => allDay;
            set
            {
                if (allDay != value)
                {
                    allDay = value;
                    NotifyPropertyChanged();

                    NormalizeToAllDayTimes();
                    NotifyPropertyChanged(nameof(Duration));
                }
            }
        }

        [Display(ResourceType = typeof(Model.Resources.Calendar.EventResource), Name = nameof(Model.Resources.Calendar.EventResource.Block))]
        public bool Block
        {
            get
            {
                return block;
            }
            set
            {
                block = value;
                NotifyPropertyChanged();
            }
        }

        //[Display(ResourceType = typeof(Model.Resources.Calendar.EventResource), Name = nameof(Model.Resources.Calendar.EventResource.EventReminders))]
        public List<EventReminder> EventReminders { get; set; }


        public bool CustomRecurrence
        {
            get
            {
                return customRecurrence;
            }
            set
            {
                if (this.customRecurrence != value)
                {
                    this.customRecurrence = value;
                    NotifyPropertyChanged();
                }
            }
        }

        public Guid? CustomRecurrenceId
        {
            get
            {
                return customRecurrenceId;
            }
            set
            {
                if (this.customRecurrenceId != value)
                {
                    this.customRecurrenceId = value;
                    NotifyPropertyChanged();
                }
            }
        }

        public string CustomRecurrenceRule
        {
            get
            {
                return customRecurrenceRule;
            }
            set
            {
                if (this.customRecurrenceRule != value)
                {
                    this.customRecurrenceRule = value;
                    NotifyPropertyChanged();
                }
            }
        }

        public DateTime? ReminderTimeUtc
        {
            get
            {
                return reminderTimeUtc;
            }
            set
            {
                if (this.reminderTimeUtc != value)
                {
                    this.reminderTimeUtc = value;
                    NotifyPropertyChanged();
                }
            }
        }

        public bool? ReminderDismissed
        {
            get
            {
                return reminderDismissed;
            }
            set
            {
                if (this.reminderDismissed != value)
                {
                    this.reminderDismissed = value;
                    NotifyPropertyChanged();
                }
            }
        }

        [Display(ResourceType = typeof(Model.Resources.Calendar.EventResource), Name = nameof(Model.Resources.Calendar.EventResource.ReadOnly))]
        public bool ReadOnly
        {
            get
            {
                return readOnly;
            }
            set
            {
                readOnly = value;
                NotifyPropertyChanged();
            }
        }

        [NotMapped]
        public TimeSpan Duration => EndTimeUtc - StartTimeUtc;

        [NotMapped]
        [Display(ResourceType = typeof(Model.Resources.Calendar.EventResource), Name = nameof(Model.Resources.Calendar.EventResource.Summary))]
        public string Summary
        {
            get
            {
                try
                {
                    //Generates Summary based on EventTextDisplay and EventTextSeparator
                    if (EventTextDisplay == EventTextDisplay.Subject)
                    {
                        return Subject;
                    }
                    else if (EventTextDisplay == EventTextDisplay.Subject)
                    {
                        return Subject;
                    }
                    else if (EventTextDisplay == EventTextDisplay.SubjectContactName)
                    {
                        if (string.IsNullOrEmpty(Contact?.FirstLastName))
                        {
                            return Subject;
                        }
                        return string.Join(EventTextSeparator.GetValue(), Subject, Contact?.FirstLastName);
                    }
                    else if (EventTextDisplay == EventTextDisplay.ContactNameSubject)
                    {
                        if (string.IsNullOrEmpty(Contact?.FirstLastName))
                        {
                            return Subject;
                        }
                        return string.Join(EventTextSeparator.GetValue(), Contact?.FirstLastName ?? "", Subject);
                    }
                    else
                    {
                        return Subject;
                    }
                }
                catch (Exception ex)
                {
                    // Log the exception or handle it as needed
                    Console.WriteLine($"Error generating summary: {ex.Message}");
                    return Subject; // Fallback to just the subject in case of error
                }
            }
        }

        [NotMapped]
        [Display(ResourceType = typeof(Model.Resources.Calendar.EventResource), Name = nameof(Model.Resources.Calendar.EventResource.EventUsers))]
        public string EventUsersNames
        {
            get
            {
                if (EventUsers != null && EventUsers.Count > 0)
                {
                    var userNames = EventUsers
                        .Where(eu => eu.UserId.HasValue)
                        .Select(eu =>
                        {
                            if (eu.User != null && !string.IsNullOrEmpty(eu.User.FullName.Trim()))
                            {
                                return eu.User.FullName;
                            }
                            else
                            {
                                return "";
                            }
                        })
                        .ToList();

                    return string.Join("; ", userNames);
                }
                return "";
            }
        }

        [Display(ResourceType = typeof(Model.Resources.Calendar.EventResource), Name = nameof(Model.Resources.Calendar.EventResource.EventUsers))]
        public List<EventUser> EventUsers { get; set; }

        [NotMapped]
        [Display(ResourceType = typeof(Model.Resources.Calendar.EventResource), Name = nameof(Model.Resources.Calendar.EventResource.EventUsers))]
        public Guid[] EventUserIds
        {
            get
            {
                if (EventUsers != null && EventUsers.Count > 0)
                {
                    return EventUsers.Select(x => x.UserId!.Value).ToArray();
                }
                else
                {
                    return new Guid[] { Guid.Parse("11111111-1111-1111-1111-111111111111") };
                }
            }
            //set
            //{

            //}
        }

        [Display(ResourceType = typeof(Model.Resources.GeneralDisplayResource), Name = nameof(Model.Resources.GeneralDisplayResource.DateCreated))]
        [Column(TypeName = "datetime")]
        public DateTime DateCreatedUtc
        {
            get => DateTime.SpecifyKind(dateCreatedUtc, DateTimeKind.Utc);
            set
            {
                var utcValue = EnsureUtc(value);
                if (dateCreatedUtc != utcValue)
                {
                    dateCreatedUtc = utcValue;
                    NotifyPropertyChanged();
                    NotifyPropertyChanged(nameof(DateCreatedLocal));
                }
            }
        }

        [Display(ResourceType = typeof(Model.Resources.GeneralDisplayResource), Name = nameof(Model.Resources.GeneralDisplayResource.DateCreated))]
        [NotMapped]
        public DateTime? DateCreatedLocal
        {
            get
            {
                var tz = GetUserTimeZone();
                return tz == null ? null : TimeZoneInfo.ConvertTimeFromUtc(dateCreatedUtc, tz);
            }
            set
            {
                if (value == null) return;
                var tz = GetUserTimeZone();
                if (tz == null) return;

                if (value.Value.Kind != DateTimeKind.Unspecified)
                    throw new ArgumentException("Local times must be provided with DateTimeKind.Unspecified", nameof(DateCreatedLocal));

                DateCreatedUtc = TimeZoneInfo.ConvertTimeToUtc(value.Value, tz);
            }
        }

        [Display(ResourceType = typeof(Model.Resources.GeneralDisplayResource), Name = nameof(Model.Resources.GeneralDisplayResource.DateModified))]
        [Column(TypeName = "datetime")]
        public DateTime DateModifiedUtc
        {
            get => DateTime.SpecifyKind(dateModifiedUtc, DateTimeKind.Utc);
            set
            {
                var utcValue = EnsureUtc(value);
                if (dateModifiedUtc != utcValue)
                {
                    dateModifiedUtc = utcValue;
                    NotifyPropertyChanged();
                    NotifyPropertyChanged(nameof(DateModifiedLocal));
                }
            }
        }

        [Display(ResourceType = typeof(Model.Resources.GeneralDisplayResource), Name = nameof(Model.Resources.GeneralDisplayResource.DateModified))]
        [NotMapped]
        public DateTime? DateModifiedLocal
        {
            get
            {
                var tz = GetUserTimeZone();
                return tz == null ? null : TimeZoneInfo.ConvertTimeFromUtc(dateModifiedUtc, tz);
            }
            set
            {
                if (value == null) return;
                var tz = GetUserTimeZone();
                if (tz == null) return;

                if (value.Value.Kind != DateTimeKind.Unspecified)
                    throw new ArgumentException("Local times must be provided with DateTimeKind.Unspecified", nameof(DateModifiedLocal));

                DateModifiedUtc = TimeZoneInfo.ConvertTimeToUtc(value.Value, tz);
            }
        }

        [Timestamp]
        public byte[] RowVersion { get; set; }

        [NotMapped]
        public ObjectState ObjectState { get; set; }


        public event PropertyChangedEventHandler? PropertyChanged;

        private void NotifyPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            //DateModifiedUtc = DateTime.UtcNow;

            //Έγινε σχόλιο γιατί τρέχει πολύ συχνά και γίνεται Modified χωρίς λόγο.
            //if (ObjectState == ObjectState.Unchanged)
            //{
            //    ObjectState = ObjectState.Modified;
            //}

            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));

        }

        [NotMapped]
        public string UserTimeZoneId { get; set; } = string.Empty;

        [NotMapped]
        public EventTextDisplay EventTextDisplay { get; set; } = EventTextDisplay.Subject;

        [NotMapped]
        public EventTextSeparator EventTextSeparator { get; set; } = EventTextSeparator.Space;


        #region  Data Manipulation Methods


        public static Event CreateEvent(string userTimeZoneId, DateTime startTimeLocal, DateTime endTimeLocal, Guid tenantId, Guid? userId)
        {
            Event eventObj = new Event();
            eventObj.TenantId = tenantId;
            if (userId != null)
            {
                eventObj.AddNewEventUser(userId);
            }
            eventObj.UserTimeZoneId = userTimeZoneId;
            eventObj.StartTimeLocal = startTimeLocal;
            eventObj.EndTimeLocal = endTimeLocal;
            eventObj.ObjectState = ObjectState.Added;

            return eventObj;
        }

        public EventUser AddNewEventUser(Guid? userId)
        {
            //Αν υπάρχει ήδη, τότε το επιστρέφουμε.
            if (EventUsers.Where(x => x.UserId == userId).Count() > 0)
            {
                return EventUsers.Where(x => x.UserId == userId).First();
            }
            else
            {
                EventUser eventUser = new EventUser();
                eventUser.EventUserId = Guid.CreateVersion7();
                eventUser.EventId = this.EventId;
                eventUser.UserId = userId;
                eventUser.ObjectState = ObjectState.Added;
                EventUsers.Add(eventUser);

                return eventUser;
            }
        }

        public EventReminder SetReminder(ReminderTime reminder)
        {
            if (EventReminders.Count == 0)  //Αν δεν υπάρχει EventReminder.
            {
                EventReminder eventReminder = new EventReminder();
                eventReminder.EventId = this.eventId;
                eventReminder.ObjectState = ObjectState.Added;
                EventReminders.Add(eventReminder);
            }
            else  //Αν υπάρχει ήδη EventReminder
            {
                EventReminders[0].ObjectState = ObjectState.Modified;
            }

            EventReminders[0].ReminderOffset = reminder;
            EventReminders[0].ReminderTimeUtc = this.StartTimeUtc.AddMinutes(-1 * reminder.GetMinutes());

            return EventReminders[0];
        }

        private static DateTime EnsureUtc(DateTime value) => value.Kind == DateTimeKind.Utc ? value : DateTime.SpecifyKind(value, DateTimeKind.Utc);

        private void NormalizeToAllDayTimes()
        {
            if (!AllDay) return;

            var tz = GetUserTimeZone();

            if (tz != null)
            {
                var startLocal = TimeZoneInfo.ConvertTimeFromUtc(startTimeUtc, tz).Date;
                var endLocal = TimeZoneInfo.ConvertTimeFromUtc(endTimeUtc, tz).Date;

                startTimeUtc = TimeZoneInfo.ConvertTimeToUtc(startLocal, tz);
                endTimeUtc = TimeZoneInfo.ConvertTimeToUtc(endLocal.AddDays(1).Date, tz);
            }
            else
            {
                //throw new Exception("TimeZoneId is not set");
                // Fallback to UTC date boundaries
                //startTimeUtc = startTimeUtc.Date;
                //endTimeUtc = endTimeUtc.Date.AddDays(1).Date;
            }

            NotifyPropertyChanged(nameof(StartTimeUtc));
            NotifyPropertyChanged(nameof(EndTimeUtc));
            NotifyPropertyChanged(nameof(StartTimeLocal));
            NotifyPropertyChanged(nameof(EndTimeLocal));
        }

        private DateTime NormalizeUtcDateTimeForAllDay(DateTime dateTime)
        {
            if (!AllDay) return dateTime;

            var tz = GetUserTimeZone();

            if (tz != null)
            {
                var dateLocal = TimeZoneInfo.ConvertTimeFromUtc(dateTime, tz).Date;

                return TimeZoneInfo.ConvertTimeToUtc(dateLocal, tz);
            }
            else
            {
                throw new Exception("TimeZone not found");
            }
        }

        private TimeZoneInfo? cachedTimeZone;  // Cache the TimeZoneInfo to avoid repeated lookups
        private TimeZoneInfo? GetUserTimeZone()
        {
            if (string.IsNullOrEmpty(UserTimeZoneId))
                return null;

            if (cachedTimeZone == null)
            {
                try
                {
                    cachedTimeZone = TimeZoneInfo.FindSystemTimeZoneById(UserTimeZoneId);
                }
                catch (TimeZoneNotFoundException)
                {
                    return null;
                }
            }
            return cachedTimeZone;
        }

        public void RemoveReminder()
        {
            if (EventReminders.Count > 0)  //Αν υπάρχει EventReminder.
            {
                EventReminders[0].ObjectState = ObjectState.Deleted;
            }
        }

        public Event Clone()
        {
            // Create a shallow copy of the object
            var clone = (Event)this.MemberwiseClone();

            return clone;
        }

        /// <summary>
        /// Copy the Event object, but does not included Recurrence. New Guids are created for the Event and EventUsers.
        /// </summary>
        /// <returns></returns>
        public Event CopyAsNew()
        {
            //Παλιός τρόπος clone που αντιγράφει τα πάντα, ακόμα και τα ίδια Guid.
            //// Create a shallow copy of the object
            //var clone = (Event)this.MemberwiseClone();

            //// Handle reference type properties that need deep copying
            //clone.RowVersion = new byte[0];
            //clone.EventUsers = new List<EventUser>(this.EventUsers);
            //clone.EventId = Guid.CreateVersion7();

            //return clone;

            ///////////////////////////

            // Create a new instance
            var clone = new Event
            {
                // Copy primitive type properties
                EventId = Guid.CreateVersion7(),
                ObjectState = ObjectState.Added,
                TenantId = this.TenantId,
                ContactId = this.ContactId,
                EventCategoryId = this.EventCategoryId,
                EventStateId = this.EventStateId,
                Subject = this.Subject,
                Location = this.Location,
                StartTimeUtc = this.StartTimeUtc,
                StartTimeZone = this.StartTimeZone,
                EndTimeUtc = this.EndTimeUtc,
                EndTimeZone = this.EndTimeZone,
                Description = this.Description,
                AllDay = this.AllDay,
                Block = this.Block,
                CustomRecurrence = false,  //this.CustomRecurrence,  Δεν θέλουμε να αντιγράψουμε το Recurrence.
                CustomRecurrenceId = null,  //this.CustomRecurrenceId,
                CustomRecurrenceRule = "",  //this.CustomRecurrenceRule,
                ReminderTimeUtc = this.ReminderTimeUtc,
                ReminderDismissed = this.ReminderDismissed,
                ReadOnly = this.ReadOnly,
                DateCreatedUtc = DateTime.UtcNow,
                DateModifiedUtc = DateTime.UtcNow,
                UserTimeZoneId = this.UserTimeZoneId
            };

            // Deep copy EventUsers collection
            clone.EventUsers = this.EventUsers.Select(eu => new EventUser
            {
                EventUserId = Guid.CreateVersion7(),
                EventId = clone.EventId,
                UserId = eu.UserId,
                ObjectState = ObjectState.Added
            }).ToList();



            // Reference properties will be handled by EF Core when saving

            return clone;
        }
        #endregion
    }
}
