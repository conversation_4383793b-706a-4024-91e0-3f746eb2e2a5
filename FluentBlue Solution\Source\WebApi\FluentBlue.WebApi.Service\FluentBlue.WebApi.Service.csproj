﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
	<RuntimeIdentifiers>win-x64</RuntimeIdentifiers>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <InvariantGlobalization>false</InvariantGlobalization>
    <BaseOutputPath>api</BaseOutputPath>
  </PropertyGroup>

    <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
		<PublishTrimmed>false</PublishTrimmed>
		<TrimMode>partial</TrimMode>
		<SelfContained>true</SelfContained>
		<ReadyToRun>true</ReadyToRun>
		<InvariantGlobalization>false</InvariantGlobalization>
    </PropertyGroup>

  <ItemGroup>
    <Compile Remove="Auth\**" />
    <Content Remove="Auth\**" />
    <EmbeddedResource Remove="Auth\**" />
    <None Remove="Auth\**" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="AutoMapper" Version="15.0.1" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="9.0.9" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.Versioning" Version="5.1.0" />
    <PackageReference Include="Microsoft.AspNetCore.SignalR" Version="1.2.0" />
    <PackageReference Include="Sentry.Serilog" Version="5.15.0" />
    <PackageReference Include="Serilog.AspNetCore" Version="9.0.0" />
    <PackageReference Include="Serilog.Enrichers.Environment" Version="3.0.1" />
    <PackageReference Include="Serilog.Exceptions" Version="8.4.0" />
    <PackageReference Include="Serilog.Sinks.Console" Version="6.0.0" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="9.0.4" />
	<PackageReference Include="Google.Apis.Auth" Version="1.71.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\Application\FluentBlue.Application.Business\FluentBlue.Application.Business.csproj" />
    <ProjectReference Include="..\FluentBlue.WebApi.Shared\FluentBlue.WebApi.Shared.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Compile Update="Resources\GlobalResource.Designer.cs">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>GlobalResource.resx</DependentUpon>
    </Compile>
    <Compile Update="Resources\RolesResource.Designer.cs">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>RolesResource.resx</DependentUpon>
    </Compile>
    <Compile Update="Resources\UsersResource.Designer.cs">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>UsersResource.resx</DependentUpon>
    </Compile>
  </ItemGroup>

  <ItemGroup>
    <EmbeddedResource Update="Resources\GlobalResource.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>GlobalResource.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Update="Resources\RolesResource.el.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
    </EmbeddedResource>
    <EmbeddedResource Update="Resources\RolesResource.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>RolesResource.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Update="Resources\UsersResource.el.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
    </EmbeddedResource>
    <EmbeddedResource Update="Resources\UsersResource.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>UsersResource.Designer.cs</LastGenOutput>
    </EmbeddedResource>
  </ItemGroup>

</Project>
