using AutoMapper;
using FluentBlue.Application.Business;
using FluentBlue.Application.Business.Request;
using FluentBlue.Application.Business.Services;
using FluentBlue.Data.Model;
using FluentBlue.Shared.Cryptography.AES;
using FluentBlue.WebApi.Service.Controllers;
using FluentBlue.WebApi.Service.Hubs;
using FluentBlue.WebApi.Service.Services;
using FluentBlue.WebApi.Shared.Request;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Versioning;
using Microsoft.AspNetCore.ResponseCompression;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.IdentityModel.Tokens;
using Microsoft.OpenApi.Models;
using Serilog;
using Serilog.Core;
using Serilog.Events;
using Serilog.Exceptions;
using System.IO.Compression;
using System.Reflection;
using System.Runtime.CompilerServices;
using System.Text;
using System.Text.Json;
using System.Text.Json.Serialization;

namespace FluentBlue.WebApi.Service
{

    public class Program
    {
        public static void Main(string[] args)
        {
            var builder = WebApplication.CreateBuilder(args);

            // Get the assembly version
            string assemblyVersion = Assembly.GetExecutingAssembly().GetName().Version?.ToString() ?? "1.0.0";

            // Configure Serilog
            Log.Logger = new LoggerConfiguration()
                .ReadFrom.Configuration(builder.Configuration) // Read base config from appsettings.json
                .Enrich.WithExceptionDetails() // Add detailed exception information
                .Enrich.WithProperty("Application", "FluentBlue.WebApi.Service")
                //.Enrich.WithProperty("Version", assemblyVersion)
                .Enrich.WithMachineName() // Add server name
                .Enrich.WithEnvironmentName() // Add environment (Development/Production)
                .WriteTo.Console() // Console sink for local debugging
                .WriteTo.Sentry(o =>
                {
                    o.Dsn = "https://<EMAIL>/4509101428572240";
                    o.MinimumEventLevel = LogEventLevel.Error;
                    o.AttachStacktrace = true;
                    o.Debug = builder.Environment.IsDevelopment();
                    o.Environment = builder.Environment.IsDevelopment() ? "Development" : "Production";
                    o.Release = assemblyVersion;
#if Debug
                    o.TracesSampleRate = 1.0f; // Adjust the sample rate as needed
#else
                    o.TracesSampleRate = 0.1f; // Adjust the sample rate as needed
#endif
                })
                .CreateLogger();

            // Replace default logging with Serilog
            builder.Host.UseSerilog();

            //builder.Services.AddLogging();
            builder.Services.AddCors(options =>
            {
                options.AddPolicy("AllowSpecificOrigins",
                    policy =>
                    {
                        policy.AllowAnyOrigin()
                        //WithOrigins(
                        //        "https://fluentblueweb-wa.azurewebsites.net",  // Production web app
                        //        "http://localhost:5096",                       // Development web app
                        //        "https://localhost:5096",                      // Development web app with HTTPS
                        //        "https://*.ngrok-free.app"          // Development ngrok tunnel
                        //    )
                            .AllowAnyHeader()
                            .AllowAnyMethod();
                        //.AllowCredentials();
                    });
            });

            #region  Depedency Injection
            //AutoMapper
            var loggerFactory = LoggerFactory.Create(builder => builder.AddSerilog());
            var autoMapperConfig = new MapperConfiguration(cfg =>
            {
                cfg.CreateMap<FluentBlue.Data.Model.DBOs.Contacts.ContactCategoryMapping, FluentBlue.Data.Model.DTOs.ContactCategoryView>()
                    .ForMember(dest => dest.Name, opt => opt.MapFrom(src => src.ContactCategory != null ? src.ContactCategory.Name : ""))
                    .ForMember(dest => dest.Color, opt => opt.MapFrom(src => src.ContactCategory != null ? src.ContactCategory.Color : ""));
                cfg.CreateMap<FluentBlue.Data.Model.DBOs.Contacts.Contact, FluentBlue.Data.Model.DTOs.ContactView>()
                    .ForMember(dest => dest.ContactCategoryViews, opt => opt.MapFrom(src => src.ContactCategoryMappings));
                cfg.CreateMap<FluentBlue.Data.Model.DBOs.Tenants.Role, FluentBlue.Data.Model.DTOs.RoleLI>();
                cfg.CreateMap<FluentBlue.Data.Model.DBOs.Tenants.User, FluentBlue.Data.Model.DTOs.UserLI>();
                cfg.CreateMap<FluentBlue.Data.Model.DBOs.Contacts.Contact, FluentBlue.Data.Model.DTOs.ContactLI>();
                cfg.CreateMap<FluentBlue.Data.Model.DBOs.Tenants.User, FluentBlue.Data.Model.DTOs.UserView>()
                    .ForMember(dest => dest.RoleName, opt => opt.MapFrom(src => src.Role!.Name));
                cfg.CreateMap<RequestDataParameters, ReadPagedDataParameters>();
                cfg.CreateMap<RequestPagedContactsParameters, ReadPagedContactsParameters>();
                cfg.CreateMap<FluentBlue.WebApi.Shared.Request.RequestEventsParameters, FluentBlue.Application.Business.Request.RequestEventsParameters>();
            }, loggerFactory);
            AutoMapper.Mapper mapper = new Mapper(autoMapperConfig);
            builder.Services.AddSingleton<IMapper>(mapper);

            //Other services
            builder.Services.AddScoped<IWebApiCallerInfo, WebApiCallerInfo>();
            builder.Services.AddScoped<IEmailService, EmailService>();

            // Register encryption services
            builder.Services.AddSingleton<IEncryptionService, FluentBlue.Shared.Cryptography.AES.EncryptionService>();
            builder.Services.AddSingleton<IApplicationKeyService, FluentBlue.Application.Business.Services.ApplicationKeyService>();
            builder.Services.AddScoped<IDataMigrationService, FluentBlue.Application.Business.Services.DataMigrationService>();

            builder.Services.AddScoped<ITenantsBusiness>(sp => new FluentBlue.Application.Business.TenantsBusiness(new Data.Model.FluentBlueDbContext(builder.Configuration["FluentBlueConnectionString"]), mapper, sp.GetRequiredService<ILogger<TenantsBusiness>>()));
            builder.Services.AddScoped<IContactsBusiness>(sp => new FluentBlue.Application.Business.ContactsBusiness(new Data.Model.FluentBlueDbContext(builder.Configuration["FluentBlueConnectionString"]), mapper, sp.GetRequiredService<ILogger<ContactsBusiness>>()));
            builder.Services.AddScoped<IRolesBusiness>(sp => new FluentBlue.Application.Business.RolesBusiness(new Data.Model.FluentBlueDbContext(builder.Configuration["FluentBlueConnectionString"]), mapper, sp.GetRequiredService<ILogger<UsersBusiness>>()));
            builder.Services.AddScoped<IUsersBusiness>(sp => new FluentBlue.Application.Business.UsersBusiness(new Data.Model.FluentBlueDbContext(builder.Configuration["FluentBlueConnectionString"]), mapper, sp.GetRequiredService<ILogger<UsersBusiness>>(), sp.GetRequiredService<IEmailService>()));
            builder.Services.AddScoped<IEventsBusiness>(sp => new FluentBlue.Application.Business.EventsBusiness(new Data.Model.FluentBlueDbContext(builder.Configuration["FluentBlueConnectionString"]), mapper, sp.GetRequiredService<ILogger<EventsBusiness>>()));
            builder.Services.AddScoped<ISettingsBusiness>(sp => new FluentBlue.Application.Business.SettingsBusiness(new Data.Model.FluentBlueDbContext(builder.Configuration["FluentBlueConnectionString"]), mapper, sp.GetRequiredService<ILogger<SettingsBusiness>>()));
            builder.Services.AddScoped<IEventCategoriesBusiness>(sp => new FluentBlue.Application.Business.EventCategoriesBusiness(new Data.Model.FluentBlueDbContext(builder.Configuration["FluentBlueConnectionString"]), mapper, sp.GetRequiredService<ILogger<EventCategoriesBusiness>>()));
            builder.Services.AddScoped<IEventStatesBusiness>(sp => new FluentBlue.Application.Business.EventStatesBusiness(new Data.Model.FluentBlueDbContext(builder.Configuration["FluentBlueConnectionString"]), mapper, sp.GetRequiredService<ILogger<EventStatesBusiness>>()));
            builder.Services.AddScoped<IContactCategoriesBusiness>(sp => new FluentBlue.Application.Business.ContactCategoriesBusiness(new Data.Model.FluentBlueDbContext(builder.Configuration["FluentBlueConnectionString"]), mapper, sp.GetRequiredService<ILogger<ContactCategoriesBusiness>>()));
            builder.Services.AddScoped<IUserDevicesBusiness>(sp => new FluentBlue.Application.Business.UserDevicesBusiness(new Data.Model.FluentBlueDbContext(builder.Configuration["FluentBlueConnectionString"]), sp.GetRequiredService<ILogger<UserDevicesBusiness>>()));
            //add to services FluentBlueDbContext as scoped service
            //builder.Services.AddDbContext<FluentBlue.Data.Model.FluentBlueDbContext>(options => options.UseSqlServer(builder.Configuration["FluentBlueConnectionString"]));
            builder.Services.AddScoped<FluentBlue.Data.Model.FluentBlueDbContext>(sp => new FluentBlue.Data.Model.FluentBlueDbContext(builder.Configuration["FluentBlueConnectionString"]));
            builder.Services.AddSingleton<FcmPushNotificationService>();


            #endregion

            // Optimized JSON serialization settings for better performance
            builder.Services.AddControllers()
                .AddJsonOptions(options =>
                {
                    // Existing cycle handling
                    options.JsonSerializerOptions.ReferenceHandler = ReferenceHandler.IgnoreCycles;

                    // Performance optimizations
                    options.JsonSerializerOptions.PropertyNamingPolicy = JsonNamingPolicy.CamelCase;
                    options.JsonSerializerOptions.WriteIndented = false; // Reduces payload size
                    options.JsonSerializerOptions.DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull; // Skip null values

                    // Additional performance settings
                    options.JsonSerializerOptions.PropertyNameCaseInsensitive = true;
                    options.JsonSerializerOptions.NumberHandling = JsonNumberHandling.AllowReadingFromString;
                    options.JsonSerializerOptions.ReadCommentHandling = JsonCommentHandling.Skip;
                    options.JsonSerializerOptions.AllowTrailingCommas = true;
                });

            // Configure HTTP JSON options for minimal APIs and other endpoints
            builder.Services.ConfigureHttpJsonOptions(options =>
            {
                options.SerializerOptions.PropertyNamingPolicy = JsonNamingPolicy.CamelCase;
                options.SerializerOptions.WriteIndented = false;
                options.SerializerOptions.DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull;
                options.SerializerOptions.ReferenceHandler = ReferenceHandler.IgnoreCycles;
            });

            builder.Services.AddSignalR(options =>
            {
                // Optimize SignalR for better performance
                options.EnableDetailedErrors = builder.Environment.IsDevelopment();
                options.MaximumReceiveMessageSize = 1024 * 1024; // 1MB
                options.StreamBufferCapacity = 10;
                options.MaximumParallelInvocationsPerClient = 1;
            })
            .AddJsonProtocol(options =>
            {
                // Optimize SignalR JSON serialization
                options.PayloadSerializerOptions.PropertyNamingPolicy = JsonNamingPolicy.CamelCase;
                options.PayloadSerializerOptions.WriteIndented = false;
                options.PayloadSerializerOptions.DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull;
                options.PayloadSerializerOptions.ReferenceHandler = ReferenceHandler.IgnoreCycles;
            });

            //Swagger
            builder.Services.AddEndpointsApiExplorer();
            builder.Services.AddSwaggerGen(c =>
            {
                c.SwaggerDoc("v1", new OpenApiInfo { Title = "FluentBlue.WebApi", Version = "v1" });

                //To Enable authorization using Swagger (JWT)
                c.AddSecurityDefinition("Bearer", new OpenApiSecurityScheme()
                {
                    Name = "Authorization",
                    Type = SecuritySchemeType.ApiKey,
                    Scheme = "Bearer",
                    BearerFormat = "JWT",
                    In = ParameterLocation.Header,
                    Description = "JWT Authorization header using the Bearer scheme. \r\n\r\n Enter 'Bearer' [space] and then your token in the text input below.\r\n\r\nExample: \"Bearer 12345abcdef\"",
                });

                c.AddSecurityRequirement(new OpenApiSecurityRequirement
                {
                    {
                        new OpenApiSecurityScheme
                        {
                            Reference = new OpenApiReference
                            {
                                Type = ReferenceType.SecurityScheme,
                                Id = "Bearer"
                            }
                        },
                        new string[] {}
                    }
                });
            });

            //JWT Authentication
            builder.Services.AddAuthentication(option =>
            {
                option.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
                option.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;

            }).AddJwtBearer(options =>
            {
                options.TokenValidationParameters = new TokenValidationParameters
                {
                    ValidateIssuer = false,
                    ValidateAudience = false,
                    ValidateLifetime = true,
                    ValidateIssuerSigningKey = true,
                    IssuerSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(builder.Configuration["jwt:key"]!))
                };
            });

            ////WebAPI versioning
            //builder.Services.AddApiVersioning(config =>
            //{
            //    config.ReportApiVersions = true;
            //    config.AssumeDefaultVersionWhenUnspecified = true;
            //    config.DefaultApiVersion = new ApiVersion(1, 0);
            //});
            //WebAPI versioning - Updated for .NET 9
            builder.Services.AddApiVersioning(options =>
            {
                options.ReportApiVersions = true;
                options.AssumeDefaultVersionWhenUnspecified = true;
                options.DefaultApiVersion = new ApiVersion(1, 0);
                options.ApiVersionReader = ApiVersionReader.Combine(
                    new QueryStringApiVersionReader("version"),
                    new HeaderApiVersionReader("X-Version"),
                    new UrlSegmentApiVersionReader()
                );
            });

            builder.Services.AddHostedService<EventReminderService>();

            // Enhanced response compression with multiple compression providers
            builder.Services.AddResponseCompression(options =>
            {
                options.EnableForHttps = true;
                options.Providers.Add<BrotliCompressionProvider>();
                options.Providers.Add<GzipCompressionProvider>();

                // Add MIME types that should be compressed
                options.MimeTypes = ResponseCompressionDefaults.MimeTypes.Concat(new[]
                {
                    "application/json",
                    "application/javascript",
                    "application/xml",
                    "text/css",
                    "text/html",
                    "text/json",
                    "text/plain",
                    "text/xml"
                });
            });

            // Configure compression levels
            builder.Services.Configure<BrotliCompressionProviderOptions>(options =>
            {
                options.Level = CompressionLevel.Optimal;
            });

            builder.Services.Configure<GzipCompressionProviderOptions>(options =>
            {
                options.Level = CompressionLevel.Optimal;
            });

            var app = builder.Build();

            //Configuration
            builder.Configuration.SetBasePath(Directory.GetCurrentDirectory()).AddJsonFile("appsettings.json", optional: false, true).AddJsonFile($"appsettings.{app.Environment.EnvironmentName}.json", optional: true, true);

            //Cors
            app.UseCors("AllowSpecificOrigins");

            // Configure the HTTP request pipeline
            if (app.Environment.IsDevelopment())
            {
                app.UseSwagger();
                app.UseSwaggerUI();
            }

            // Configure exception handling - MUST be before other middleware to ensure exceptions are properly handled
            app.UseExceptionHandler(errorApp =>
            {
                errorApp.Run(async context =>
                {
                    var exceptionHandlerPathFeature = context.Features.Get<Microsoft.AspNetCore.Diagnostics.IExceptionHandlerPathFeature>();
                    var exception = exceptionHandlerPathFeature?.Error;

                    if (exception != null)
                    {
                        Log.Error(exception, "Unhandled exception");
                    }

                    // Return appropriate error response
                    context.Response.StatusCode = 500;
                    await context.Response.WriteAsJsonAsync(new { error = "An error occurred processing your request" });
                });
            });

            // Use response compression early in the pipeline for maximum efficiency
            app.UseResponseCompression();

            app.UseSerilogRequestLogging();
            app.UseAuthorization();
            app.UseMiddleware<WebApiCallerMiddleware>();  //Προσθέτουμε το middleware που διαβάζει το Tenant
            app.UseMiddleware<FluentBlue.WebApi.Service.Middleware.ApplicationKeyMiddleware>();  //Προσθέτουμε το middleware που διαβάζει το ApplicationKey για το Encryption.

            app.MapControllers();

            app.MapHub<NotificationHub>("/notificationHub"); // Map the hub to a URL

            app.Logger.LogInformation("Environment name:" + app.Environment.EnvironmentName);
            //app.Logger.LogInformation(app.Configuration.GetValue<string>("FluentBlueConnectionString")!.ToString());

            try
            {
                Log.Information("Starting FluentBlue.WebApi.Service");
                app.Run();
            }
            catch (Exception ex)
            {
                Log.Fatal(ex, "FluentBlue.WebApi.Service terminated unexpectedly");
            }
            finally
            {
                Log.CloseAndFlush();
            }
        }
    }
}
