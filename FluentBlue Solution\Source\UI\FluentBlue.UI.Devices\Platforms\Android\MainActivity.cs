﻿using Android.App;
using Android.Content;
using Android.Content.PM;
using Android.OS;
using AndroidX.Core.App;
using AndroidX.Core.Content;
using FluentBlue.UI.Devices.Services;
using FluentBlue.WebApi.Client;
using Microsoft.Extensions.Caching.Hybrid;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;

namespace FluentBlue.UI.Devices
{
    [Activity(Theme = "@style/Maui.SplashTheme", MainLauncher = true, ConfigurationChanges = ConfigChanges.ScreenSize | ConfigChanges.Orientation | ConfigChanges.UiMode | ConfigChanges.ScreenLayout | ConfigChanges.SmallestScreenSize | ConfigChanges.Density)]
    public class MainActivity : MauiAppCompatActivity
    {
        internal static readonly string channelID = "ReminderChannel";
        internal static readonly int notificationID = 101;

        protected override void OnCreate(Bundle savedInstanceState)
        {
            base.OnCreate(savedInstanceState);
            if (ContextCompat.CheckSelfPermission(this, Android.Manifest.Permission.PostNotifications) == Permission.Denied)
            {
                ActivityCompat.RequestPermissions(this, new String[] { Android.Manifest.Permission.PostNotifications }, 1);
            }

            CreateNotificationChannel();
            
            // Get device token from Preferences if it exists
            string deviceToken = string.Empty;
            if (Preferences.ContainsKey("DeviceToken"))
            {
                deviceToken = Preferences.Get("DeviceToken", string.Empty);
                
                //Store the device token to cache so as to be used in MainLayout and be registered in UserDevices in database.
                HybridCache? cache = MainApplication.Services.GetService<HybridCache>();
                Task.Run(() => cache!.SetAsync("DeviceToken", deviceToken));
            }
            else
            {
                // If token doesn't exist yet, request it from Firebase
                Firebase.FirebaseApp.InitializeApp(this);
                // This will trigger FirebaseService.OnNewToken() callback
                var task = Firebase.Messaging.FirebaseMessaging.Instance.GetToken();
                // You can process the token in the completion listener
                task.AddOnCompleteListener(new OnTokenCompleteListener());
            }
        }

        protected override void OnNewIntent(Intent intent)
        {
            base.OnNewIntent(intent);

            if (intent.Extras != null)
            {
                foreach (string? key in intent.Extras.KeySet() ?? new List<string>())
                {
                    if (key == "EventId")
                    {
                        string? eventId = intent.Extras.GetString(key);
                        //Navigate to EventComponent with the EventId



                        //string idValue = intent.Extras.GetString(key);
                        //if (Preferences.ContainsKey("NavigationID"))
                        //    Preferences.Remove("NavigationID");

                        //Preferences.Set("NavigationID", idValue);

                        ////WeakReferenceMessenger.Default.Send(new PushNotificationReceived("test"));
                    }
                }
            }
        }

        private void CreateNotificationChannel()
        {
            if (OperatingSystem.IsOSPlatformVersionAtLeast("android", 26))
            {
                var channel = new NotificationChannel(channelID, "Test Notification Channel", NotificationImportance.Default);

                var notificaitonManager = (NotificationManager)GetSystemService(Android.Content.Context.NotificationService);
                notificaitonManager.CreateNotificationChannel(channel);
            }
        }

        // Add this class to handle token retrieval completion
        private class OnTokenCompleteListener : Java.Lang.Object, Android.Gms.Tasks.IOnCompleteListener
        {
            public void OnComplete(Android.Gms.Tasks.Task task)
            {
                if (task.IsSuccessful)
                {
                    string deviceToken = task.Result.ToString();
                    if (Preferences.ContainsKey("DeviceToken"))
                    {
                        Preferences.Remove("DeviceToken");
                    }
                    Preferences.Set("DeviceToken", deviceToken);

                    //Store the device token to cache so as to be used in MainLayout and be registered in UserDevices in database.
                    HybridCache? cache = MainApplication.Services.GetService<HybridCache>();
                    Task.Run(()=> cache!.SetAsync("DeviceToken", deviceToken));

                    //TODO: Fix below: It will fail because user is not authenticated, and webapi call will return 401.
                    //Task.Run(() => RegisterDeviceToken(deviceToken)); // Register the device token to database for the user, but it will fail because user is not authenticated, and webapi call will return 401.

                    // Now you can use the token as needed, e.g., send it to your server
                    System.Diagnostics.Debug.WriteLine($"FCM Token: {deviceToken}");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"Failed to get FCM token: {task.Exception?.Message}");
                }
            }

            private async Task RegisterDeviceToken(string token)
            {
                var logger = MainApplication.Services.GetService<ILogger<OnTokenCompleteListener>>();
                try
                {
                    var registrationService = MainApplication.Services.GetService<UserDevicesWebApiClient>();
                    if (registrationService != null)
                    {
                        await registrationService.RegisterDeviceAsync(token, DeviceInfo.Platform.ToString());
                        logger?.LogInformation("Device token registered successfully from MainActivity.");
                    }
                }
                catch (Exception ex)
                {
                    logger?.LogError(ex, "Failed to register device token from MainActivity.");
                }
            }
        }
    }
}