﻿using Blazored.FluentValidation;
using FluentBlue.Data.Model;
using FluentBlue.Data.Model.DBOs.Calendar;
using FluentBlue.Data.Model.DBOs.Settings;
using FluentBlue.Data.Model.DBOs.Tenants;
using FluentBlue.Data.Model.DBOs.Validators;
using FluentBlue.Data.Model.DTOs;
using FluentBlue.UI.Main.Auth;
using FluentBlue.UI.Main.Shared;
using FluentBlue.WebApi.Client;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Forms;
using Microsoft.Extensions.Logging;
using Microsoft.FluentUI.AspNetCore.Components;
using Microsoft.JSInterop;
using Syncfusion.Blazor.Popups;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using Microsoft.Extensions.Caching.Hybrid;

namespace FluentBlue.UI.Main.Components
{
    public partial class UserDialog
    {
        //General
        [Parameter] public UserDialogInput? Content { get; set; } = new UserDialogInput() { User = new User() };
        private EditContext userContext = new EditContext(typeof(Data.Model.DBOs.Tenants.User));
        [CascadingParameter]
        public FluentDialog CurrentDialog { get; set; } = default!;
        private FluentValidationValidator? fluentValidationValidator = new FluentValidationValidator();
        UserSetting? userSetting;

        //User
        //private RoleLI? selectedRole;
        private string? selectedRoleId;
        private List<RoleLI>? roles = new List<RoleLI>();
        private FluentTextField emailTxtField = new FluentTextField();
        private FluentSelect<RoleLI> roleIdSelect = new FluentSelect<RoleLI>();

        protected override async Task OnAfterRenderAsync(bool firstRender)
        {
            try
            {
                if (firstRender)
                {
                    // Reads the UserSettings from cache or database
                    var userSettingCacheOptions = new HybridCacheEntryOptions { LocalCacheExpiration = TimeSpan.FromMinutes(500) };

                    this.userSetting = await cache.GetOrCreateAsync(Keywords.UserSetting,
                        async cancel =>
                        {
                            UsersWebApiClient usersWebApiClient = new UsersWebApiClient(httpClient, usersWebApiClientLogger);
                            return await usersWebApiClient.GetUserSettings(AuthenticatedUserData.UserId);
                        }, userSettingCacheOptions);

                    //Γεμίζει με data το combobox για το Role.
                    Task<List<Data.Model.DTOs.RoleLI>> rolesTask = new WebApi.Client.RolesWebApiClient(httpClient, rolesWebApiClientLogger).GetRolesList(AuthenticatedUserData.TenantId);
                    this.roles = await rolesTask;
                    //this.roles.Insert(0, RoleLI.Empty);


                    this.emailTxtField!.ReadOnly = this.Content!.User.ObjectState != ObjectState.Added;
                    //this.Content.RoleId = this.roles.First().RoleId;

                    this.SetDataToUI();
                    StateHasChanged();
                }
                else
                {
                }
            }
            catch (ApplicationException ex)
            {
                logger.LogError(ex, ex.Message);
                new ErrorNotifier(dialogService).ShowInfo(ex.Message, "");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                await new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
            finally
            {

            }
        }

        private void GetDataFromUI()
        {
            //TODO: Αυτή η function υπάρχει για ορισμένα controls όπως τα FluentCombobox όπου το @bind-Value δεν δουλεύει σωστά και αντί για το Value επιστρέφει το Text του control.
            //Αυτό θα πρέπει να αλλάξει και να φύγει η function αυτή.
            //this.Content!.RoleId = this.selectedRole?.RoleId ?? Guid.Empty;
            //this.Content!.RoleId = this.roleIdSelect!.SelectedOption?.RoleId;
            this.Content!.User.RoleId = this.selectedRoleId != null ? Guid.Parse(this.selectedRoleId) : Guid.Empty;
            this.Content!.User.Role = null;  //Το κάνουμε null για να μην διαφέρει από το RoleId.
        }

        private void SetDataToUI()
        {
            //this.selectedRole = this.roles!.Where(x => x.RoleId == this.Content!.RoleId).FirstOrDefault()!;
            if (this.Content!.User.RoleId.HasValue)
            {
                //this.selectedRole = this.roles!.Where(x => x.RoleId == this.Content!.RoleId).FirstOrDefault()!;
                this.selectedRoleId = this.roles!.Where(x => x.RoleId == this.Content!.User.RoleId).FirstOrDefault()?.RoleId.ToString();
            }
            else
            {
                //this.selectedRole = RoleLI.Empty;
                this.selectedRoleId = Guid.Empty.ToString();
            }
        }

        private async Task CancelBtn_OnClick()
        {
            try
            {
                await this.CurrentDialog.CancelAsync();
            }
            catch (ApplicationException ex)
            {
                logger.LogError(ex, ex.Message);
                new ErrorNotifier(dialogService).ShowInfo(ex.Message, "");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                await new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
        }

        private async Task DeleteBtn_OnClick()
        {
            try
            {
                var dialog = await dialogService.ShowConfirmationAsync(GlobalResource.DeleteDataConfirmation, GlobalResource.Yes, GlobalResource.No, GlobalResource.DeleteDataTitle);
                DialogResult result = await dialog.Result;
                await dialog.CloseAsync();

                if (result.Cancelled == false)
                {
                    UsersWebApiClient usersWebApiClient = new UsersWebApiClient(httpClient, usersWebApiClientLogger);
                    await usersWebApiClient.DeleteUser(this.Content!.User.UserId);
                    await this.CurrentDialog.CloseAsync();
                }
            }
            catch (ApplicationException ex)
            {
                logger.LogError(ex, ex.Message);
                new ErrorNotifier(dialogService).ShowInfo(ex.Message, "");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                await new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
        }

        private async Task SaveBtn_OnClick()
        {
            await this.Save(true);
        }

        private async Task Save(bool close)
        {
            try
            {
                this.GetDataFromUI();

                if (this.Content!.User.ObjectState != ObjectState.Added)
                {
                    this.Content.User.ObjectState = ObjectState.Modified;
                }
                else
                {
                    this.Content.User.DateCreatedUtc = DateTime.UtcNow;
                }
                this.Content.User.DateModifiedUtc = DateTime.UtcNow;

                if (await this.ValidateData())
                {
                    UsersWebApiClient usersWebApiClient = new UsersWebApiClient(httpClient, usersWebApiClientLogger);
                    User? tempUser = await usersWebApiClient.CreateOrUpdateUser(this.Content.User);
                    if (tempUser == null)
                    {
                        //Αν έγινε αποθήκευση αλλά δεν επέστρεψε το αντικείμενο, τότε εμφανίζουμε γενικό σφάλμα και κλείνουμε το dialog.
                        await new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
                        await this.CurrentDialog.CloseAsync(this.Content.User);
                    }

                    this.Content.User = tempUser!;
                    this.Content.User.UserTimeZoneId = this.userSetting!.TimeZone;

                    if (close)
                    {
                        await this.CurrentDialog.CloseAsync(this.Content.User);
                    }
                    else
                    {
                        this.Content.User.ObjectState = ObjectState.Unchanged;
                        this.StateHasChanged();
                    }
                }
            }
            catch (ApplicationException ex)
            {
                logger.LogError(ex, ex.Message);
                new ErrorNotifier(dialogService).ShowInfo(ex.Message, "");
            }
            catch (Exception ex)
            {
                if (ex.GetType() == typeof(ApplicationException))
                {
                    await new ErrorNotifier(dialogService).ShowErrorAsync(ex.Message, "");
                }
                else
                {
                    await new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
                }
                logger.LogError(ex, ex.Message);
            }
        }

        private async Task<bool> ValidateData()
        {
            try
            {
                //UserValidator validator = new UserValidator();
                //this.fluentValidationValidator.Validator = validator;
                bool valid = this.fluentValidationValidator!.Validate();
                if (valid == false)
                {
                    // Convert error messages to HTML bullet list
                    string errorMessage = GlobalResource.CorrectInvalidFields;
                    RenderFragment errorRF = FluentBlue.Shared.Utilities.ValidationErrorsToBulletsConverter.ConvertValidationErrorsToBullets(errorMessage, this.fluentValidationValidator.GetFailuresFromLastValidation().Select(e => e.ErrorMessage).ToArray(), "");

                    // Show errors in dialog
                    await dialogService.ShowDialogAsync(errorRF, new DialogParameters
                    {
                        ShowTitle = false,
                        ShowDismiss = false,
                        DialogType = Microsoft.FluentUI.AspNetCore.Components.DialogType.MessageBox,
                        PrimaryAction = UI.Main.GlobalResource.Close,
                        SecondaryAction = "",
                        Modal = true,
                        PreventDismissOnOverlayClick = true
                    });
                }

                return valid;
            }
            catch (Exception)
            {
                throw;
            }
        }

        private async Task SetPassword()
        {
            try
            {
                var dialog = await this.dialogService.ShowDialogAsync<SetPasswordDialog>(new DialogParameters()
                {
                    Height = "350px",
                    Title = Resources.SetPasswordDialogResource.Title,
                    PreventDismissOnOverlayClick = true,
                    PreventScroll = true,
                });

                var result = await dialog.Result;
                if (!result.Cancelled && result.Data != null)
                {
                    string password = result?.Data.ToString() ?? string.Empty;
                    this.Content!.User.Password = password;
                }
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                await new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
        }

    }
}
