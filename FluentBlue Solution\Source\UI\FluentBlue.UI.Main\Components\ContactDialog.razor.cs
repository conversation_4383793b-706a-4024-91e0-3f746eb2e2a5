﻿using Blazored.FluentValidation;
using FluentBlue.Data.Model;
using FluentBlue.Data.Model.DBOs.Calendar;
using FluentBlue.Data.Model.DBOs.Contacts;
using FluentBlue.Data.Model.DBOs.Settings;
using FluentBlue.Data.Model.DBOs.Validators;
using FluentBlue.Data.Model.DTOs;
using FluentBlue.Shared.Utilities;
using FluentBlue.UI.Main.Auth;
using FluentBlue.UI.Main.Shared;
using FluentBlue.WebApi.Client;
using FluentBlue.WebApi.Shared.Request;
using FluentValidation.Results;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Forms;
using Microsoft.EntityFrameworkCore.Metadata.Internal;
using Microsoft.Extensions.Caching.Hybrid;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.FluentUI.AspNetCore.Components;
using Microsoft.JSInterop;
using Syncfusion.Blazor;
using Syncfusion.Blazor.Popups;
using Syncfusion.Blazor.RichTextEditor;
using System.ComponentModel;
using System.Net.Http;
using System.Runtime.CompilerServices;

namespace FluentBlue.UI.Main.Components
{
    public partial class ContactDialog
    {
        //Contact
        //private string? ContactId { get; set; }
        [Parameter] public ContactDialogInput Content { get; set; } = default!;
        private EditContext contactContext = new EditContext(typeof(Data.Model.DBOs.Contacts.Contact));
        List<Option<string?>> emailTypeOptions = Enum.GetValues<Data.Model.EmailType>().Cast<Data.Model.EmailType?>().Select(x => new Option<string?> { Text = x!.Value.ToString(), Value = x.Value.ToString() }).ToList();
        List<Option<string?>> phoneTypeOptions = Enum.GetValues<Data.Model.PhoneType>().Cast<Data.Model.PhoneType?>().Select(x => new Option<string?> { Text = x!.Value.ToString(), Value = x.Value.ToString() }).ToList();
        List<Option<string?>> addressTypeOptions = Enum.GetValues<Data.Model.AddressType>().Cast<Data.Model.AddressType?>().Select(x => new Option<string?> { Text = x!.Value.ToString(), Value = x.Value.ToString() }).ToList();
        private IEnumerable<ContactCategory>? selectedContactCategories = new List<ContactCategory>();
        private List<ContactCategory>? contactCategories = new List<ContactCategory>();
        private FluentAutocomplete<ContactCategory> contactCategoriesSelect = new FluentAutocomplete<ContactCategory>();
        private Dictionary<Guid, bool> menuStates = new();
        private static readonly HybridCacheEntryOptions userSettingCacheOptions = new HybridCacheEntryOptions { LocalCacheExpiration = TimeSpan.FromMinutes(500) };
        private static readonly HybridCacheEntryOptions contactCategoriesCacheOptions = new HybridCacheEntryOptions { LocalCacheExpiration = TimeSpan.FromMinutes(60) };


        //Contact Events
        private List<Data.Model.DBOs.Calendar.Event> events = new List<Data.Model.DBOs.Calendar.Event>();
        GridSort<Data.Model.DBOs.Calendar.Event> startTimeGridSort = GridSort<Data.Model.DBOs.Calendar.Event>.ByDescending(x => x.StartTimeLocal);
        FluentDataGrid<Data.Model.DBOs.Calendar.Event>? eventsDataGrid;

        //General
        [CascadingParameter]
        public FluentDialog CurrentDialog { get; set; } = default!;
        IDialogReference? dialog;
        private FluentValidationValidator? fluentValidationValidator;
        UserSetting? userSetting = null;
        private List<ToolbarItemModel> toolbarItems = new List<ToolbarItemModel>()
        {
            new ToolbarItemModel() { Command = ToolbarCommand.Bold },
            new ToolbarItemModel() { Command = ToolbarCommand.Italic },
            new ToolbarItemModel() { Command = ToolbarCommand.Underline },
            new ToolbarItemModel() { Command = ToolbarCommand.StrikeThrough },
            new ToolbarItemModel() { Command = ToolbarCommand.Separator },
            new ToolbarItemModel() { Command = ToolbarCommand.FontColor },
            new ToolbarItemModel() { Command = ToolbarCommand.BackgroundColor },
            new ToolbarItemModel() { Command = ToolbarCommand.Separator },
            new ToolbarItemModel() { Command = ToolbarCommand.Formats },
            new ToolbarItemModel() { Command = ToolbarCommand.Alignments },
            new ToolbarItemModel() { Command = ToolbarCommand.Separator },
            new ToolbarItemModel() { Command = ToolbarCommand.OrderedList },
            new ToolbarItemModel() { Command = ToolbarCommand.UnorderedList },
            new ToolbarItemModel() { Command = ToolbarCommand.Separator },
            new ToolbarItemModel() { Command = ToolbarCommand.Outdent },
            new ToolbarItemModel() { Command = ToolbarCommand.Indent },
            new ToolbarItemModel() { Command = ToolbarCommand.Separator },
            new ToolbarItemModel() { Command = ToolbarCommand.CreateLink },
            new ToolbarItemModel() { Command = ToolbarCommand.CreateTable },
            new ToolbarItemModel() { Command = ToolbarCommand.Separator },
            new ToolbarItemModel() { Command = ToolbarCommand.Undo },
            new ToolbarItemModel() { Command = ToolbarCommand.Redo }
        };
        private bool isSaving = false;
        private bool isDeleting = false;


        protected override async void OnAfterRender(bool firstRender)
        {
            try
            {
                if (firstRender)
                {
                    //Διαβάζει τα UserSetting
                    //var userSettingCacheOptions = new HybridCacheEntryOptions { LocalCacheExpiration = TimeSpan.FromMinutes(500) };
                    this.userSetting = await cache.GetOrCreateAsync(Keywords.UserSetting,
                        async cancel =>
                        {
                            UsersWebApiClient usersWebApiClient = new UsersWebApiClient(httpClient, usersWebApiClientLogger);
                            return await usersWebApiClient.GetUserSettings(AuthenticatedUserData.UserId);
                        }, userSettingCacheOptions);

                    //Διαβάζει τα ContactCategories.
                    //var contactCategoriesCacheOptions = new HybridCacheEntryOptions { LocalCacheExpiration = TimeSpan.FromMinutes(60) };
                    this.contactCategories = await cache.GetOrCreateAsync(
                        Keywords.ContactCategories,
                        async cancel =>
                        {
                            ContactCategoriesWebApiClient contactCategoriesWebApiClient = new ContactCategoriesWebApiClient(httpClient, contactCategoresWebApiClientLogger);
                            return await contactCategoriesWebApiClient.GetAllContactCategories(AuthenticatedUserData.TenantId);
                        },
                        contactCategoriesCacheOptions,
                        new[] { "Deletable" });

                    this.Content.Contact.NotifyChildrenPropertyChangedEnabled = true;  //Ενεργοποιούμε το NotifyPropertyChanged στα child entities του Contact.

                    this.SetDataToUI();
                    StateHasChanged();
                }
                else
                {

                }
            }
            catch (Exception ex) when (ex is HttpRequestException || ex is TaskCanceledException)
            {
                this.isSaving = false;
                await new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ConnectivityIssue, "");
            }
            catch (ApplicationException ex)
            {
                logger.LogError(ex, ex.Message);
                await new ErrorNotifier(dialogService).ShowErrorAsync(ex.Message, "");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                await new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
            finally
            {

            }
        }

        private async Task CloseBtnOnClick()
        {
            try
            {
                await this.CurrentDialog.CancelAsync();
            }
            catch (ApplicationException ex)
            {
                logger.LogError(ex, ex.Message);
                await new ErrorNotifier(dialogService).ShowErrorAsync(ex.Message, "");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                await new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
        }

        private async Task DeleteBtnOnClick()
        {
            try
            {
                this.isDeleting = true;

                await this.CloseAllMenus();

                var dialog = await dialogService.ShowConfirmationAsync(GlobalResource.DeleteDataConfirmation, GlobalResource.Yes, GlobalResource.No, GlobalResource.DeleteDataTitle);
                DialogResult result = await dialog.Result;
                await dialog.CloseAsync();

                if (result.Cancelled == false)
                {
                    ContactsWebApiClient contactsWebApiClient = new ContactsWebApiClient(httpClient, contactsWebApiClientLogger);
                    await contactsWebApiClient.DeleteContact(this.Content!.Contact.ContactId);
                    await this.CurrentDialog.CloseAsync();
                }
            }
            catch (Exception ex) when (ex is HttpRequestException || ex is TaskCanceledException)
            {
                this.isDeleting = false;
                await new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ConnectivityIssue, "");
            }
            catch (ApplicationException ex)
            {
                this.isDeleting = false;
                if (ex.Data.Contains("DbConcurrencyException"))
                {
                    await new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.DbConcurrencyExceptionMessage, "");
                    await CurrentDialog.CloseAsync(Content.Contact);
                }
                else
                {
                    logger.LogError(ex, ex.Message);
                    new ErrorNotifier(dialogService).ShowInfo(ex.Message, "");
                }
            }
            catch (Exception ex)
            {
                this.isDeleting = false;
                if (ex.Data.Contains("DbConcurrencyException"))
                {
                    await new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.DbConcurrencyExceptionMessage, "");
                    await CurrentDialog.CloseAsync(this.Content.Contact);
                }
                else
                {
                    logger.LogError(ex, ex.Message);
                    await new ErrorNotifier(dialogService).ShowErrorAsync(ex.Message, "");
                }
            }
            finally
            {
                this.isDeleting = false;
            }
        }

        private async Task SaveBtnOnClick()
        {
            try
            {
                await CloseAllMenus();
                await this.Save(true);
            }
            catch (Exception ex) when (ex is HttpRequestException || ex is TaskCanceledException)
            {
                this.isSaving = false;
                await new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ConnectivityIssue, "");
            }
            catch (ApplicationException ex)
            {
                new ErrorNotifier(dialogService).ShowInfo(ex.Message, "");
            }
            catch (Exception ex)
            {
                await new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
                logger.LogError(ex, ex.Message);
            }
        }

        private async Task Save(bool close)
        {
            this.isSaving = true;
            try
            {
                await this.CloseAllMenus();
                this.GetDateFromUI();

                #region  Set the DateCreated and DateModified for Phones, Addresses, Emails of Contact
                if (this.Content.Contact!.ObjectState != ObjectState.Added)
                {
                    this.Content.Contact.ObjectState = ObjectState.Modified;
                }
                else
                {
                    this.Content.Contact.DateCreatedUtc = DateTime.UtcNow;
                }
                this.Content.Contact.DateModifiedUtc = DateTime.UtcNow;

                foreach (var phone in this.Content!.Contact.Phones)
                {
                    if (phone.ObjectState == ObjectState.Added)
                    {
                        phone.DateCreatedUtc = DateTime.UtcNow;
                        phone.DateModifiedUtc = DateTime.UtcNow;
                    }
                    else if (phone.ObjectState == ObjectState.Modified)
                    {
                        phone.DateModifiedUtc = DateTime.UtcNow;
                    }
                }

                foreach (var address in this.Content!.Contact.Addresses)
                {
                    if (address.ObjectState == ObjectState.Added)
                    {
                        address.DateCreatedUtc = DateTime.UtcNow;
                        address.DateModifiedUtc = DateTime.UtcNow;
                    }
                    else if (address.ObjectState == ObjectState.Modified)
                    {
                        address.DateModifiedUtc = DateTime.UtcNow;
                    }
                }

                foreach (var email in this.Content!.Contact.Emails)
                {
                    if (email.ObjectState == ObjectState.Added)
                    {
                        email.DateCreatedUtc = DateTime.UtcNow;
                        email.DateModifiedUtc = DateTime.UtcNow;
                    }
                    else if (email.ObjectState == ObjectState.Modified)
                    {
                        email.DateModifiedUtc = DateTime.UtcNow;
                    }
                }
                #endregion

                if (await this.ValidateData())
                {
                    ContactsWebApiClient contactsWebApiClient = new ContactsWebApiClient(httpClient, contactsWebApiClientLogger);
                    Contact? tempContact = await contactsWebApiClient.CreateOrUpdateContact(this.Content.Contact);
                    if (tempContact == null)
                    {
                        //Αν έγινε αποθήκευση αλλά δεν επέστρεψε το αντικείμενο, τότε εμφανίζουμε γενικό σφάλμα και κλείνουμε το dialog.
                        await new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
                        await this.CurrentDialog.CloseAsync(this.Content.Contact);
                    }

                    this.Content.Contact = tempContact!;

                    if (close)
                    {
                        await this.CurrentDialog.CloseAsync(this.Content.Contact);
                    }
                    else
                    {
                        this.Content!.Contact.ObjectState = ObjectState.Unchanged;

                        this.StateHasChanged();
                    }
                }
            }
            catch (Exception ex) when (ex is HttpRequestException || ex is TaskCanceledException)
            {
                this.isSaving = false;
                await new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ConnectivityIssue, "");
            }
            catch (ApplicationException ex)
            {
                this.isSaving = false;
                if (ex.Data.Contains("DbConcurrencyException"))
                {
                    await new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.DbConcurrencyExceptionMessage, "");
                    if (close)
                    {
                        await CurrentDialog.CloseAsync(this.Content.Contact);
                    }
                }
                else
                {
                    logger.LogError(ex, ex.Message);
                    new ErrorNotifier(dialogService).ShowInfo(ex.Message, "");
                }
            }
            catch (Exception ex)
            {
                this.isSaving = false;
                await new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
                logger.LogError(ex, ex.Message);
            }
            finally
            {
                this.isSaving = false;
            }
        }

        private void SetDataToUI()
        {
            #region  ContactCategoryMappings
            //Δημιουργεί τα EventUsers που προστέθηκαν.
            foreach (ContactCategoryMapping contactCategoryMapping in this.Content!.Contact.ContactCategoryMappings.ToList())
            {
                ContactCategory? contactCategory = this.contactCategories!.Where(x => x.ContactCategoryId == contactCategoryMapping.ContactCategoryId).FirstOrDefault();
                if (contactCategory != null)
                {
                    this.selectedContactCategories = this.selectedContactCategories!.Append(contactCategory);
                }
            }
            #endregion
        }

        private void GetDateFromUI()
        {
            #region Διαβάζει τα ContactCategoryMappings
            //Δημιουργεί τα EventUsers που προστέθηκαν.
            if (this.selectedContactCategories != null)
            {
                foreach (ContactCategory contactCategory in this.selectedContactCategories)
                {
                    //Αν ο χρήστης δεν εισαχθει στο ContactCategories, τότε το δημιουργει.
                    if (!this.Content.Contact.ContactCategoryMappings.Where(x => x.ContactCategoryId == contactCategory.ContactCategoryId).Any())
                    {
                        ContactCategoryMapping contactCategoryMapping = this.Content.Contact.NewContactCategoryMapping();
                        contactCategoryMapping.ContactId = this.Content.Contact.ContactId;
                        contactCategoryMapping.ContactCategoryId = contactCategory.ContactCategoryId;
                    }
                }
            }

            //Διαγράφει τα EventUsers που προστέθηκαν.
            foreach (ContactCategoryMapping contactCategoryMapping in this.Content.Contact.ContactCategoryMappings.ToList())
            {
                //Αν δεν υπάρχει στους επιλεγμένους Users.
                if (!this.selectedContactCategories!.Where(x => x.ContactCategoryId == contactCategoryMapping.ContactCategoryId).Any())
                {
                    contactCategoryMapping.ObjectState = ObjectState.Deleted;
                }
            }
            #endregion
        }

        private async Task<bool> ValidateData()
        {
            //ContactValidator validator = new ContactValidator();
            //this.fluentValidationValidator.Validator = validator;
            bool valid = this.fluentValidationValidator!.Validate();
            if (valid == false)
            {
                // Convert error messages to HTML bullet list
                string errorMessage = GlobalResource.CorrectInvalidFields;
                RenderFragment errorRF = FluentBlue.Shared.Utilities.ValidationErrorsToBulletsConverter.ConvertValidationErrorsToBullets(errorMessage, this.fluentValidationValidator.GetFailuresFromLastValidation().Select(e => e.ErrorMessage).ToArray(), "");

                // Show errors in dialog
                await dialogService.ShowDialogAsync(errorRF, new DialogParameters
                {
                    ShowTitle = false,
                    ShowDismiss = false,
                    DialogType = Microsoft.FluentUI.AspNetCore.Components.DialogType.MessageBox,
                    PrimaryAction = UI.Main.GlobalResource.Close,
                    SecondaryAction = "",
                    Modal = true,
                    PreventDismissOnOverlayClick = true
                });
            }

            return valid;
        }

        #region  Emails
        private async Task AddEmailBtnOnClick()
        {
            try
            {
                this.CloseAllMenu();  //Κλείνει όλα τα μενού πριν ανοίξει το νέο.

                //Αν δεν υπαρχει άλλο νέο email με κενό address.
                if (this.Content!.Contact.Emails.Where(x => x.EmailAddress == "" && x.ObjectState == ObjectState.Added).Count() == 0)
                {
                    this.Content.Contact.NewContactEmail();
                }
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                await new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
        }

        private async Task RemoveEmailBtnOnClick(Guid contactEmailId)
        {
            try
            {
                ContactEmail contactEmail = this.Content!.Contact.Emails.Where(x => x.ContactEmailId == contactEmailId).First();
                if (contactEmail.ObjectState == ObjectState.Added)
                {
                    this.Content.Contact.Emails.Remove(contactEmail);
                }
                else
                {
                    contactEmail.ObjectState = ObjectState.Deleted;
                }
            }
            catch (ApplicationException ex)
            {
                new ErrorNotifier(dialogService).ShowInfo(ex.Message, "");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                await new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
        }

        private async Task SendEmailBtnOnClick(Guid contactEmailId)
        {
            try
            {
                ContactEmail contactEmail = this.Content!.Contact.Emails.Where(x => x.ContactEmailId == contactEmailId).First();
                await sendEmail.OpenEmailForm(contactEmail.EmailAddress, "", "");
            }
            catch (ApplicationException ex)
            {
                new ErrorNotifier(dialogService).ShowInfo(ex.Message, "");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                await new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
        }
        #endregion

        #region  Phones
        private async Task AddPhoneBtnOnClick()
        {
            try
            {
                this.CloseAllMenu();  //Κλείνει όλα τα μενού πριν ανοίξει το νέο.

                //Αν δεν υπαρχει άλλο νέο Phone με κενό address.
                if (this.Content!.Contact.Phones.Where(x => x.PhoneNumber == "" && x.ObjectState == ObjectState.Added).Count() == 0)
                {
                    ContactPhone phone = this.Content.Contact.NewContactPhone();
                    phone.UserTimeZoneId = this.userSetting!.TimeZone;
                    phone.PropertyChanged += OnPropertyChanged;
                }
            }
            catch (ApplicationException ex)
            {
                new ErrorNotifier(dialogService).ShowInfo(ex.Message, "");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                await new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
        }

        private async Task CallPhoneBtnOnClick(Guid contactPhoneId)
        {
            try
            {
                ContactPhone contactPhone = this.Content!.Contact.Phones.Where(x => x.ContactPhoneId == contactPhoneId).First();
                string cleanPhoneNumber = contactPhone.PhoneNumber.Replace(" ", "").Replace("-", "");
                await performPhoneCall.PlacePhoneCall(cleanPhoneNumber);
            }
            catch (ApplicationException ex)
            {
                new ErrorNotifier(dialogService).ShowInfo(ex.Message, "");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                await new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
        }

        private async Task RemovePhoneBtnOnClick(Guid contactPhoneId)
        {
            try
            {
                ContactPhone contactPhone = this.Content!.Contact.Phones.Where(x => x.ContactPhoneId == contactPhoneId).First();
                if (contactPhone.ObjectState == ObjectState.Added)
                {
                    this.Content.Contact.Phones.Remove(contactPhone);
                }
                else
                {
                    contactPhone.ObjectState = ObjectState.Deleted;
                }
            }
            catch (ApplicationException ex)
            {
                new ErrorNotifier(dialogService).ShowInfo(ex.Message, "");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                await new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
        }
        #endregion

        #region  Addresses
        private async Task AddAddressBtnOnClick()
        {
            try
            {
                this.CloseAllMenu();  //Κλείνει όλα τα μενού πριν ανοίξει το νέο.

                //Αν δεν υπαρχει άλλο νέο Address με κενό address.
                if (this.Content!.Contact.Addresses.Where(x => x.Address == "" && x.ObjectState == ObjectState.Added).Count() == 0)
                {
                    this.Content.Contact.NewContactAddress();
                }
            }
            catch (ApplicationException ex)
            {
                new ErrorNotifier(dialogService).ShowInfo(ex.Message, "");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                await new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
        }

        private async Task NavigateToAddressBtnOnClick(Guid contactAddressId)
        {
            try
            {
                ContactAddress contactAddress = this.Content!.Contact.Addresses.Where(x => x.ContactAddressId == contactAddressId).First();
                await mapsNavigation.NavigateToAddress(contactAddress.Address);
            }
            catch (ApplicationException ex)
            {
                new ErrorNotifier(dialogService).ShowInfo(ex.Message, "");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                await new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
        }

        private async Task RemoveAddressBtnOnClick(Guid contactAddressId)
        {
            try
            {
                ContactAddress contactAddress = this.Content!.Contact.Addresses.Where(x => x.ContactAddressId == contactAddressId).First();
                if (contactAddress.ObjectState == ObjectState.Added)
                {
                    this.Content.Contact.Addresses.Remove(contactAddress);
                }
                else
                {
                    contactAddress.ObjectState = ObjectState.Deleted;
                }
            }
            catch (ApplicationException ex)
            {
                new ErrorNotifier(dialogService).ShowInfo(ex.Message, "");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                await new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
        }
        #endregion

        private async Task ToggleMenu(Guid id)
        {
            try
            {
                this.CloseAllMenu();

                // Toggle the clicked menu
                if (!menuStates.ContainsKey(id))
                {
                    menuStates[id] = true;
                }
                else
                {
                    menuStates[id] = !menuStates[id];
                }
            }
            catch (ApplicationException ex)
            {
                new ErrorNotifier(dialogService).ShowInfo(ex.Message, "");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                await new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
        }

        private void CloseAllMenu()
        {
            // Ensure only one menu is open at a time (optional)
            foreach (var key in menuStates.Keys.ToList())
            {
                menuStates[key] = false;
            }
        }

        #region Contact Events
        private async Task OnEditEvent(Guid eventId)
        {
            try
            {
                Event eventObj = this.events.Where(x => x.EventId == eventId).First();
                await ShowEvent(eventObj);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                await new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
        }

        private async Task OnDeleteEvent(Guid eventId)
        {
            try
            {
                var dialog = await dialogService.ShowConfirmationAsync(GlobalResource.DeleteDataConfirmation, GlobalResource.Yes, GlobalResource.No, GlobalResource.DeleteDataTitle);
                DialogResult result = await dialog.Result;
                await dialog.CloseAsync();
                if (result.Cancelled == false)
                {
                    EventsWebApiClient eventsWebApiClient = new EventsWebApiClient(httpClient, eventsWebApiClientLogger);
                    await eventsWebApiClient.DeleteEvent(eventId, WebApi.Shared.Request.RecurrentEventHandlingType.Current);

                    this.events.Remove(events.Where(x => x.EventId == eventId).First());
                }
            }
            catch (Exception ex) when (ex is HttpRequestException || ex is TaskCanceledException)
            {
                await new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ConnectivityIssue, "");
            }
            catch (ApplicationException ex)
            {
                if (ex.Data.Contains("DbConcurrencyException"))
                {
                    await new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.DbConcurrencyExceptionMessage, "");
                }
                else
                {
                    logger.LogError(ex, ex.Message);
                    new ErrorNotifier(dialogService).ShowInfo(ex.Message, "");
                }
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                await new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
        }

        private async Task ShowEvent(Data.Model.DBOs.Calendar.Event eventObj)
        {
            try
            {
                string dialogWidth = "1000px", dialogHeight = "90%";
                if (ScreenSizeTracker.CurrentBreakpoint == "xs" || ScreenSizeTracker.CurrentBreakpoint == "sm")
                {
                    dialogWidth = "100%";
                    dialogHeight = "100%";
                }

                //εμφανίζει το dialog
                DialogParameters<FluentBlue.UI.Main.Shared.EventDialogInput> parameters = new()
                {
                    ShowTitle = true,
                    OnDialogResult = dialogService.CreateDialogCallback(this, OnEventDialogResult),
                    Title = FluentBlue.UI.Main.Components.Resources.EventDialogResource.Title,
                    PrimaryAction = "",  //GlobalResource.Save,
                    SecondaryAction = "", //=GlobalResource.Cancel,
                    Width = dialogWidth,
                    Height = dialogHeight,
                    TrapFocus = false,
                    Modal = true,
                    //PreventScroll = true,
                    PreventDismissOnOverlayClick = true,
                    ShowDismiss = false,
                    Alignment = HorizontalAlignment.Center
                };
                EventDialogInput eventDialogInput = new EventDialogInput() { Event = eventObj, RecurrentEventHandlingType = WebApi.Shared.Request.RecurrentEventHandlingType.Current, Restricted = true };
                dialog = await dialogService.ShowDialogAsync<UI.Main.Components.EventDialog>(eventDialogInput, parameters);
                DialogResult? result = await dialog.Result;
            }
            catch (Exception ex) when (ex is HttpRequestException || ex is TaskCanceledException)
            {
                await new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ConnectivityIssue, "");
            }
            catch (ApplicationException ex)
            {
                if (ex.Data.Contains("DbConcurrencyException"))
                {
                    await new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.DbConcurrencyExceptionMessage, "");
                }
                else
                {
                    logger.LogError(ex, ex.Message);
                    new ErrorNotifier(dialogService).ShowInfo(ex.Message, "");
                }
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                await new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
        }

        private async Task OnEventDialogResult(DialogResult result)
        {
            try
            {
                if (result.Cancelled == false)
                {
                    await this.FillContactEvents();
                }
                await dialog!.CloseAsync();  //Βάζουμε να κλείσει το dialog 2η φορά γιατί μέσα από το EventForm δεν δουλεύει.
            }
            catch (Exception ex) when (ex is HttpRequestException || ex is TaskCanceledException)
            {
                await new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ConnectivityIssue, "");
            }
            catch (ApplicationException ex)
            {
                logger.LogError(ex, ex.Message);
                new ErrorNotifier(dialogService).ShowInfo(ex.Message, "");
            }
        }

        private async Task FillContactEvents()
        {
            this.eventsDataGrid?.SetLoadingState(true);

            EventsWebApiClient eventsWebApiClient = new EventsWebApiClient(httpClient, eventsWebApiClientLogger);
            List<Data.Model.DBOs.Calendar.Event> contactEvents = await eventsWebApiClient.GetAllEventsOfContact(this.Content!.Contact.ContactId);

            //Ρυθμίζει το TimeZone στα Events.
            foreach (var e in contactEvents)
            {
                e.UserTimeZoneId = this.userSetting!.TimeZone;
            }

            this.events = contactEvents;

            this.eventsDataGrid?.SetLoadingState(false);
            this.StateHasChanged();
        }
        #endregion

        private async void OnTabChange(FluentTab tab)
        {
            try
            {
                await this.CloseAllMenus();

                //Αν το Contact δεν είναι νέο.
                if (this.Content!.Contact.ObjectState != ObjectState.Added)
                {
                    await FillContactEvents();
                }
            }
            catch (Exception ex) when (ex is HttpRequestException || ex is TaskCanceledException)
            {
                await new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ConnectivityIssue, "");
            }
            catch (ApplicationException ex)
            {
                logger.LogError(ex, ex.Message);
                new ErrorNotifier(dialogService).ShowInfo(ex.Message, "");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                await new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
        }

        private async void EventsDataGridOnRowClick(FluentDataGridRow<Event> e)
        {
            try
            {
                await ShowEvent(e.Item!);
            }
            catch (ApplicationException ex)
            {
                logger.LogError(ex, ex.Message);
                new ErrorNotifier(dialogService).ShowInfo(ex.Message, "");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                await new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
        }

        private async void EventsDataGridOnRowDoubleClick(FluentDataGridRow<Event> e)
        {
            try
            {
                if (this.Content.Restricted == false)
                {
                    await ShowEvent(e.Item!);
                }
            }
            catch (ApplicationException ex)
            {
                logger.LogError(ex, ex.Message);
                new ErrorNotifier(dialogService).ShowInfo(ex.Message, "");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                await new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
        }


        private async void OnPropertyChanged(object? sender, PropertyChangedEventArgs e)
        {
            try
            {
                if (sender is IObjectState objectStateObject)
                {
                    if (objectStateObject.ObjectState == ObjectState.Unchanged)
                    {
                        objectStateObject.ObjectState = ObjectState.Modified;
                    }
                }

                //// Handle property change
                //if (sender is EventState category)
                //{
                //    if (category.ObjectState == ObjectState.Unchanged)
                //    {
                //        category.ObjectState = ObjectState.Modified;
                //    }
                //}
            }
            catch (ApplicationException ex)
            {
                logger.LogError(ex, ex.Message);
                new ErrorNotifier(dialogService).ShowInfo(ex.Message, "");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                await new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
        }

        private async Task ContactCategoriesSelectOnSelectedOptionsChanged(IEnumerable<ContactCategory>? selectedContactCategories)
        {
            try
            {
                this.selectedContactCategories = selectedContactCategories;
            }
            catch (ApplicationException ex)
            {
                logger.LogError(ex, ex.Message);
                new ErrorNotifier(dialogService).ShowInfo(ex.Message, "");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                await new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
        }

        private async Task CloseAllMenus()
        {
            try
            {
                // Clear all menu states to close any open menus
                foreach (var key in menuStates.Keys.ToList())
                {
                    menuStates[key] = false;
                }
                StateHasChanged();
            }
            catch (ApplicationException ex)
            {
                logger.LogError(ex, ex.Message);
                new ErrorNotifier(dialogService).ShowInfo(ex.Message, "");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                await new ErrorNotifier(dialogService).ShowErrorAsync(GlobalResource.ErrorOccured, "");
            }
        }

    }
}