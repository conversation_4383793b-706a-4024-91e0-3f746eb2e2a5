@page "/login"
@layout EmptyLayout

@using FluentBlue.Shared
@using FluentBlue.UI.Main.Auth
@using FluentBlue.WebApi.Client
@using Microsoft.AspNetCore.Components.Forms
@using Microsoft.Extensions.Localization

@inject IStringLocalizer<FluentBlue.UI.Main.Pages.Resources.LoginResource> loginResource
@inject AuthenticationWebApiClient authenticationWebApiClient
@inject NavigationManager navigationManager
@inject ILoginService loginService
@inject ILogger<Login> logger
@inject IDialogService dialogService

<div style="background-size: cover; background-position: center;  height: 100vh; width: 100%; background-image: url('_content/FluentBlue.UI.Main/images/login-background.png')">
    <FluentStack Orientation="Orientation.Vertical" HorizontalAlignment="HorizontalAlignment.Center" VerticalAlignment="VerticalAlignment.Center" Style="height:100vh">
        <FluentCard Width="350px" Height="auto">
            <EditForm Model="@loginData" OnValidSubmit="@OnSignIn">
                <DataAnnotationsValidator />

                <FluentStack Orientation="Orientation.Vertical" HorizontalAlignment="HorizontalAlignment.Center">
                    <FluentLabel Typo="Typography.H1">@Resources.LoginResource.Title</FluentLabel>
                    <FluentLabel>Sign in to start your session</FluentLabel>
                    <FluentTextField Id="usernameTxtField" @bind-Value="@loginData.Username" Placeholder="Username" Maxlength="30" Autofocus="true" Style="width:85%;" Class="mb-1" AutoComplete="off">
                        <FluentIcon Value="@(new Icons.Regular.Size16.Mail())" Color="@Color.Neutral" Slot="end" />
                    </FluentTextField>
                    <div style="width:85%;">
                        <FluentValidationMessage For="@(() => loginData.Username)" />
                    </div>

                    <FluentTextField Id="passwordTxtField" @bind-Value="@loginData.Password"  TextFieldType="TextFieldType.Password" Maxlength="30" Style="width:85%;" Class="mb-1" AutoComplete="off" Placeholder="Password">
                        <FluentIcon Value="@(new Icons.Regular.Size16.LockClosed())" Color="@Color.Neutral" Slot="end" />
                    </FluentTextField>
                    <div style="width:85%;">
                        <FluentValidationMessage For="@(() => loginData.Password)" />
                    </div>

                    <FluentButton Type="@ButtonType.Submit" Loading="@loginInProgress" Appearance="@Appearance.Accent">@loginResource.GetString("signInButton.Text")</FluentButton>
                    <FluentLabel Color="Color.Warning">@loginResultMessage</FluentLabel>
                </FluentStack>
            </EditForm>
            <br />
            <p>
                <FluentAnchor Href="/forgot-password" Appearance="Appearance.Hypertext">I forgot my password</FluentAnchor>
            </p>
            <p>
                <FluentAnchor Href="/register" Appearance="Appearance.Hypertext">Register a new membership</FluentAnchor>
            </p>
        </FluentCard>
    </FluentStack>

</div>

<FluentDialogProvider />
<FluentTooltipProvider />
<FluentToastProvider MaxToastCount="10" />

