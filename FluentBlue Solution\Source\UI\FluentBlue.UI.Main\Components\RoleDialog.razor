﻿@implements IDialogContentComponent<Data.Model.DBOs.Tenants.Role>

@using Blazored.FluentValidation
@using FluentBlue.Data.Model.DBOs.Tenants
@using FluentBlue.Data.Model.DTOs
@using FluentBlue.Shared.Utilities
@using Microsoft.AspNetCore.Components.Forms
@using Microsoft.JSInterop
@using Microsoft.Extensions.Caching.Hybrid

@inject HttpClient httpClient
@inject NavigationManager navManager
@inject IDialogService dialogService
@inject ILogger<FluentBlue.UI.Main.Components.RoleDialog> logger
@inject ILogger<FluentBlue.WebApi.Client.UsersWebApiClient> usersWebApiClientLogger
@inject ILogger<FluentBlue.WebApi.Client.RolesWebApiClient> rolesWebApiClientLogger
@inject IFormFactor formFactor
@inject IJSRuntime JS
@inject HybridCache cache

<FluentDialogHeader Class="hidden"></FluentDialogHeader>

<FluentDialogBody Class="overflow-x-hidden overflow-y-auto">
    <FluentLabel Typo="Typography.H3" Style="font-weight: 400" Class="mb-4">@Resources.RoleDialogResource.Title</FluentLabel>
    <EditForm id="editForm" Model="@Content">
        <ChildContent Context="context2">
            <FluentValidationValidator @ref="fluentValidationValidator" DisableAssemblyScanning="false" />

            <FluentToolbar Orientation="Orientation.Horizontal" Class="px-0 pb-3 bg-transparent w-full">
                <FluentButton IconStart="@(new Icons.Regular.Size16.Save())" Appearance="Appearance.Accent" OnClick="SaveBtn_OnClick"><span class="xs:hidden sm:hidden">@Resources.RoleDialogResource.SaveBtn_Text</span></FluentButton>
                <FluentButton OnClick="DeleteBtn_OnClick"><FluentIcon Value="@(new Icons.Regular.Size16.Delete())" Color="Color.Error" Slot="start" /><span class="xs:hidden sm:hidden">@Resources.RoleDialogResource.DeleteBtn_Text</span></FluentButton>   @* Color="var(--error)" *@
                <FluentButton IconStart="@(new Icons.Regular.Size16.Dismiss())" OnClick="CancelBtn_OnClick"><span class="xs:hidden sm:hidden">@Resources.RoleDialogResource.CancelBtn_Text</span></FluentButton>
            </FluentToolbar>

            <FluentGrid Spacing="1">
                <FluentGridItem xs="12">
                    <FluentTextField @bind-Value="@Content!.Name" Label="@Resources.RoleDialogResource.Name" Maxlength="100" Class="w-full" Required="true" AutoComplete="off" />
                    <FluentValidationMessage For="@(() => Content.Name)" />
                    <div class="h-4" />
                </FluentGridItem>

                <FluentGridItem xs="12">
                    <FluentCheckbox @bind-Value="@Content!.CanAdministerRoles" Label="@Resources.RoleDialogResource.CanAdministerRoles" />
                    <FluentValidationMessage For="@(() => Content.CanAdministerRoles)" />
                </FluentGridItem>

                <FluentGridItem xs="12">
                    <FluentCheckbox @bind-Value="@Content!.CanAdministerUsers" Label="@Resources.RoleDialogResource.CanAdministerUsers" />
                    <FluentValidationMessage For="@(() => Content.CanAdministerUsers)" />
                </FluentGridItem>

                <FluentGridItem xs="12">
                    <FluentCheckbox @bind-Value="@Content!.CanViewOthersAppointments" Label="@Resources.RoleDialogResource.CanViewOthersAppointments" />
                    <FluentValidationMessage For="@(() => Content.CanViewOthersAppointments)" />
                </FluentGridItem>

                <FluentGridItem xs="12">
                    <FluentCheckbox @bind-Value="@Content!.CanEditApplicationSettings" Label="@Resources.RoleDialogResource.CanEditApplicationSettings" />
                    <FluentValidationMessage For="@(() => Content.CanEditApplicationSettings)" />
                </FluentGridItem>

               
            </FluentGrid>

        </ChildContent>
    </EditForm>
</FluentDialogBody>
